<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="referrer" content="no-referrer" />
  <meta name="viewport" content="width=device-width,initial-scale=1.0" />
  <meta http-equiv="pragma" content="no-cache" />
  <meta http-equiv="cache-control" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <meta name="showBottomNavBar" content="false" lk-config> 
  <script type="text/javascript" src="https://lf1-cdn-tos.bytegoofy.com/goofy/lark/op/h5-js-sdk-1.5.23.js"></script>
  <title>
    <%= process.env.SETTING.PROJECT_NAME %>
  </title>
  <script>
    // 判断飞书登录-mergesSys
    if (location.href.slice(-17) === '?autoLogin=feishu') {
      sessionStorage.autoLogin = 'feishu'
      location.href = location.href.slice(0, -17)
    }
  </script>
  <script type="text/javascript">
    if (location.search) {
      const hasBackUrl = !!location.search.slice(1).split('&').find(it => it.slice(0, 7) === 'backUrl')
      if (hasBackUrl) {
        location.href = location.origin + '/login' + location.search
      }
    }
  </script>
  <script type="text/javascript">
    let localPathname = location.pathname.replace(/\//g, '')
    localPathname = localPathname
      .replace('host-dev-', '')
      .replace('dev-', '')
      .replace(/(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})-/, '')

    window.QIANKUN_SETTING = <%= JSON.stringify(process.env.SETTING) %>
      window.QIANKUN_SETTING.localPathname = localPathname
    window.QIANKUN_SETTING.BASE_URL = "<%= process.env.VUE_APP_MODE === 'production' ? process.env.SETTING.BASE_URL_PRO : process.env.SETTING.BASE_URL_DEV %>"

    // if (['finance'].indexOf(localPathname) !== -1) {
    //   window.QIANKUN_SETTING.BASE_URL = "<%= process.env.SETTING.BASE_URL_TMP_DEV %>"
    // }
  </script>
  <link rel="shortcut icon" href="https://yujiahui.oss-cn-hangzhou.aliyuncs.com/static/project/hermes/favicon.png"
    type="image/x-icon">
</head>

<body class="qiankun-body">
  <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-6.16.0.min.js"></script>
  <script src="/lib/yjh-watermark.js?v=1.1"></script>
  <!-- <script src="https://static.syounggroup.com/web-static/vue/2.6.12/vue.min.js"></script>
    <script src="https://static.syounggroup.com/web-static/vuex/3.6.2/vuex.min.js"></script>
    <script src="https://static.syounggroup.com/web-static/vue-router/3.2.0/vue-router.min.js"></script>
    <script src="https://static.syounggroup.com/web-static/element-ui/2.15.1/index.min.js"></script>
    <script src="https://static.syounggroup.com/web-static/axios/0.21.1/axios.js"></script> -->
  <div id="qiankunRoot"></div>
  <link rel='stylesheet' href='/font/iconfont.css?v=1.1' />
  <!-- built files will be auto injected -->
</body>

</html>