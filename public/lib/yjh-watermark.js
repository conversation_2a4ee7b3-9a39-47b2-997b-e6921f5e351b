(function (root, factory) {
  if (typeof define === 'function' && define.amd) {
    /* AMD. Register as an anonymous module.
       *define([], factory); */
    define([], factory())
  } else if (typeof module === 'object' && module.exports) {
    /* Node. Does not work with strict CommonJS, but
      // only CommonJS-like environments that support module.exports,
      // like Node. */
    module.exports = factory()
  } else {
    /* Browser globals (root is window) */
    root.watermark = factory()
  }
})(this, function () {
  var watermark = {}
  var id = 'svgDom_123412415'
  var setwatermark = function (settings) {
    var defaultSettings = {
      // 灏忔按鍗扮殑id鍓嶇紑
      watermark_txt: settings.watermark_txt || '请拿外传', // 水印字体
      watermark_txt_sub: settings.watermark_txt_sub || '', // 水印第二行文本
      watermark_color: settings.watermark_color || 'rgba(153, 153, 153, 0.2)', // 水印字体颜色
      watermark_fontsize: '16px', // 水印字体大小
      watermark_width: settings.watermark_width || 80, // 水印宽度
      watermark_height: settings.watermark_height || 80 // 水印高度
    }

    // 濡傛灉鍏冪礌瀛樺湪鍒欑Щ闄�
    var watermark_element = document.getElementById(id)
    if (watermark_element !== null) {
      document.body.removeChild(watermark_element)
    }

    var can = document.createElement('canvas')
    var itemWidth = defaultSettings.watermark_width
    var itemHight = defaultSettings.watermark_height

    can.width = itemWidth * 2
    can.height = itemHight * 2
    can.style.display = 'none'

    var cans = can.getContext('2d')

    // 灏嗙敾甯冧腑蹇冪偣杩佺Щ鍒扮涓€妯″潡鐨勪腑蹇�
    cans.translate(itemWidth / 2, itemHight / 2)

    cans.font = defaultSettings.watermark_fontsize + ' Microsoft Yahei'
    cans.fillStyle = defaultSettings.watermark_color
    cans.textAlign = 'center'
    cans.textBaseline = 'middle'

    cans.save()
    cans.rotate((-15 * Math.PI) / 180)
    cans.fillText(defaultSettings.watermark_txt, 0, 0)
    cans.fillText(defaultSettings.watermark_txt_sub, 0, 20)
    cans.restore()

    // 鍥炲鍒版病鏈夋棆杞箣鍓嶇殑鐢诲竷
    // 灏嗙敾甯冧腑蹇冭縼绉诲埌鍙充笅妯″潡鐨勪腑蹇�
    cans.translate(itemWidth, itemHight)
    cans.rotate((-15 * Math.PI) / 180)
    cans.fillText(defaultSettings.watermark_txt, 0, 0)
    cans.fillText(defaultSettings.watermark_txt_sub, 0, 20)

    var watermarkerDiv = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
    var url = can.toDataURL('image/png')
    var styleStr =
      'width:100%;height:100%;position:fixed;opacity:0.5;top:0;left:0;pointer-events:none;z-index:999999;background-image:url(' +
      url +
      ');background-repeat:repeat;'
    watermarkerDiv.setAttribute('style', styleStr)
    watermarkerDiv.setAttribute('id', id)
    document.body.appendChild(watermarkerDiv)

    // // 鐩戝惉鍒犻櫎鎴栬€呭睘鎬т慨鏀�
    var MutationObserver =
      window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver
    if (MutationObserver) {
      var mo = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
          if (mutation.type === 'childList' && !document.getElementById(id)) {
            mo.disconnect()
            mo = null
            setwatermark(settings)
          } else if (mutation.type === 'attributes') {
            var item = document.getElementById(id)
            if (item && item.getAttribute('style') !== styleStr) {
              mo.disconnect()
              mo = null
              setwatermark(settings)
            }
          }
        })
      })

      // 鐩戝惉Body锛岀湅鏄惁鍏冪礌鍒犻櫎
      mo.observe(document.body, {
        childList: true
      })
      // 鐩戝惉鍏冪礌灞炴€х殑淇敼
      var item = document.getElementById(id)
      if (item) {
        mo.observe(item, {
          attributes: true
        })
      }
    }
  }

  // 姘村嵃鍒濆鍖�
  // 璇ユ柟娉曞彧鍏佽璋冪敤涓€娆�
  watermark.init = function (settings) {
    setwatermark(settings)
    // window.onresize = function() {
    //   setwatermark(settings);
    // };
  }

  // 鎵嬪姩鍔犺浇姘村嵃
  watermark.load = function (settings) {
    setwatermark(settings)
  }

  return watermark
})
