const webpack = require('webpack')
const SETTING = require('./setting')
const path = require('path')

function resolve (dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  pages: {
    index: 'src/main.js',
    preview: 'src/preview.js'
  },
  devServer: {
    disableHostCheck: true,
    port: 80,
    headers: { // 重点1: 允许跨域访问子应用页面
      'Access-Control-Allow-Origin': '*'
    },
    proxy: {
      '/top.json': {
        target: 'https://testhermes.syounggroup.com'
      }
    }
  },
  filenameHashing: true,
  configureWebpack: config => {
    const plugins = [
      new webpack.DefinePlugin({
        'process.env': {
          SETTING: JSON.stringify(SETTING)
        }
      })
    ]
    config.performance = { // 打包文件大小配置
      maxEntrypointSize: 10000000,
      maxAssetSize: 30000000
    }
    return {
      plugins
    }
  },
  chainWebpack: config => {
    // 别名
    config.resolve.alias
      .set('@', resolve('src'))
    // 使用CDN
    // config.externals({
    //   vue: 'Vue',
    //   'vue-router': 'VueRouter',
    //   'element-ui': 'ELEMENT',
    //   vuex: 'Vuex',
    //   axios: 'axios'
    // })
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
  }
}
