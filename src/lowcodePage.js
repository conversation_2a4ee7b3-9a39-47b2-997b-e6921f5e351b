import Vue from 'vue'
import lwAxios from 'lowcodelib/utils/axios'
export default () => {
  Vue.use(lwAxios)
  const menus = window.QIANKUN_DATA.user_info.menus.filter((it) => it.isShow !== '0' && it.type === 'router' && it.lowCodeKey)
  menus.forEach(it => {
    window.QIANKUN_DATA.errorPage.push({
      path: it.href,
      component: {
        name: it.code,
        render (h) {
          return h('lwRender', { props: { code: it.lowCodeKey, vm: this } })
        }
      }
    })
  })
}
