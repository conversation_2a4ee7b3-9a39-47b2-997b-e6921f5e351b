/**
 * 内存监控和管理系统
 * 提供内存使用情况监控、预警和自动清理功能
 */

class MemoryMonitor {
  constructor (options = {}) {
    this.options = {
      // 内存阈值配置（MB）
      warningThreshold: options.warningThreshold || 800, // 警告阈值
      criticalThreshold: options.criticalThreshold || 1200, // 严重阈值
      maxThreshold: options.maxThreshold || 1500, // 最大阈值

      // 监控间隔（毫秒）
      monitorInterval: options.monitorInterval || 30000, // 30秒

      // 是否启用自动清理
      autoCleanup: options.autoCleanup !== false,

      // 回调函数
      onWarning: options.onWarning || null,
      onCritical: options.onCritical || null,
      onMaxExceeded: options.onMaxExceeded || null
    }

    this.isMonitoring = false
    this.monitorTimer = null
    this.memoryHistory = []
    this.maxHistoryLength = 100
    this.cleanupCallbacks = new Set()

    // 绑定方法
    this.checkMemory = this.checkMemory.bind(this)
  }

  /**
   * 开始内存监控
   */
  startMonitoring () {
    if (this.isMonitoring) {
      return
    }

    this.isMonitoring = true
    this.monitorTimer = setInterval(this.checkMemory, this.options.monitorInterval)

    console.log('内存监控已启动')

    // 立即执行一次检查
    this.checkMemory()
  }

  /**
   * 停止内存监控
   */
  stopMonitoring () {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false
    if (this.monitorTimer) {
      clearInterval(this.monitorTimer)
      this.monitorTimer = null
    }

    console.log('内存监控已停止')
  }

  /**
   * 检查内存使用情况
   */
  checkMemory () {
    const memoryInfo = this.getMemoryInfo()
    if (!memoryInfo) {
      return null
    }

    // 记录历史数据
    this.recordMemoryHistory(memoryInfo)

    // 检查阈值
    this.checkThresholds(memoryInfo)

    return memoryInfo
  }

  /**
   * 获取内存信息
   */
  getMemoryInfo () {
    if (!window.performance || !window.performance.memory) {
      return null
    }

    const memory = window.performance.memory
    const memoryInfo = {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
      timestamp: Date.now(),
      usagePercent: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
    }

    return memoryInfo
  }

  /**
   * 记录内存历史
   */
  recordMemoryHistory (memoryInfo) {
    this.memoryHistory.push(memoryInfo)

    // 限制历史记录长度
    if (this.memoryHistory.length > this.maxHistoryLength) {
      this.memoryHistory.shift()
    }
  }

  /**
   * 检查内存阈值
   */
  checkThresholds (memoryInfo) {
    const { used } = memoryInfo

    if (used >= this.options.maxThreshold) {
      this.handleMaxThresholdExceeded(memoryInfo)
    } else if (used >= this.options.criticalThreshold) {
      this.handleCriticalThreshold(memoryInfo)
    } else if (used >= this.options.warningThreshold) {
      this.handleWarningThreshold(memoryInfo)
    }
  }

  /**
   * 处理警告阈值
   */
  handleWarningThreshold (memoryInfo) {
    console.warn(`内存使用警告: ${memoryInfo.used}MB (${memoryInfo.usagePercent}%)`)

    if (this.options.onWarning) {
      this.options.onWarning(memoryInfo)
    }

    // 触发轻量级清理
    if (this.options.autoCleanup) {
      this.performLightCleanup()
    }
  }

  /**
   * 处理严重阈值
   */
  handleCriticalThreshold (memoryInfo) {
    console.error(`内存使用严重: ${memoryInfo.used}MB (${memoryInfo.usagePercent}%)`)

    if (this.options.onCritical) {
      this.options.onCritical(memoryInfo)
    }

    // 触发深度清理
    if (this.options.autoCleanup) {
      this.performDeepCleanup()
    }
  }

  /**
   * 处理最大阈值超出
   */
  handleMaxThresholdExceeded (memoryInfo) {
    console.error(`内存使用超出最大阈值: ${memoryInfo.used}MB (${memoryInfo.usagePercent}%)`)

    if (this.options.onMaxExceeded) {
      this.options.onMaxExceeded(memoryInfo)
    } else {
      // 默认行为：显示警告并建议刷新页面
      this.showMemoryWarningDialog(memoryInfo)
    }
  }

  /**
   * 显示内存警告对话框
   */
  showMemoryWarningDialog (memoryInfo) {
    if (window.Vue && window.Vue.prototype.$confirm) {
      window.Vue.prototype.$confirm(
        `内存使用过高 (${memoryInfo.used}MB)，可能影响系统性能。建议刷新页面释放内存。`,
        '内存警告',
        {
          confirmButtonText: '刷新页面',
          cancelButtonText: '继续使用',
          type: 'warning'
        }
      ).then(() => {
        window.location.reload()
      }).catch(() => {
        // 用户选择继续使用，执行强制清理
        this.performEmergencyCleanup()
      })
    } else {
      // 降级处理
      if (confirm(`内存使用过高 (${memoryInfo.used}MB)，是否刷新页面？`)) {
        window.location.reload()
      }
    }
  }

  /**
   * 轻量级清理
   */
  performLightCleanup () {
    console.log('执行轻量级内存清理')

    // 清理缓存
    this.clearCache()

    // 触发垃圾回收（如果可用）
    this.forceGarbageCollection()

    // 执行注册的清理回调
    this.executeCleanupCallbacks('light')
  }

  /**
   * 深度清理
   */
  performDeepCleanup () {
    console.log('执行深度内存清理')

    // 执行轻量级清理
    this.performLightCleanup()

    // 清理未使用的子应用
    this.cleanupUnusedApps()

    // 执行深度清理回调
    this.executeCleanupCallbacks('deep')
  }

  /**
   * 紧急清理
   */
  performEmergencyCleanup () {
    console.log('执行紧急内存清理')

    // 执行深度清理
    this.performDeepCleanup()

    // 清理所有可清理的资源
    this.executeCleanupCallbacks('emergency')

    // 强制垃圾回收
    this.forceGarbageCollection()
  }

  /**
   * 清理缓存
   */
  clearCache () {
    // 清理Vue组件缓存
    if (window.Vue && window.Vue.config.optionMergeStrategies) {
      // 清理可能的组件缓存
    }

    // 清理图片缓存
    if (window.URL && window.URL.revokeObjectURL) {
      // 这里需要根据具体应用的缓存策略来实现
    }
  }

  /**
   * 清理未使用的子应用
   */
  cleanupUnusedApps () {
    if (window.QIANKUN_DATA && window.QIANKUN_DATA.destroyChild) {
      try {
        window.QIANKUN_DATA.destroyChild()
      } catch (error) {
        console.error('清理未使用子应用失败:', error)
      }
    }
  }

  /**
   * 强制垃圾回收
   */
  forceGarbageCollection () {
    if (window.gc && typeof window.gc === 'function') {
      try {
        window.gc()
        console.log('强制垃圾回收执行完成')
      } catch (error) {
        console.warn('强制垃圾回收失败:', error)
      }
    }
  }

  /**
   * 注册清理回调
   */
  registerCleanupCallback (callback, priority = 'normal') {
    this.cleanupCallbacks.add({ callback, priority })
  }

  /**
   * 注销清理回调
   */
  unregisterCleanupCallback (callback) {
    this.cleanupCallbacks.forEach(item => {
      if (item.callback === callback) {
        this.cleanupCallbacks.delete(item)
      }
    })
  }

  /**
   * 执行清理回调
   */
  executeCleanupCallbacks (level) {
    this.cleanupCallbacks.forEach(({ callback, priority }) => {
      try {
        if (level === 'emergency' ||
            (level === 'deep' && priority !== 'light') ||
            (level === 'light' && priority === 'light')) {
          callback(level)
        }
      } catch (error) {
        console.error('执行清理回调失败:', error)
      }
    })
  }

  /**
   * 获取内存趋势
   */
  getMemoryTrend (minutes = 5) {
    const cutoffTime = Date.now() - (minutes * 60 * 1000)
    const recentHistory = this.memoryHistory.filter(item => item.timestamp > cutoffTime)

    if (recentHistory.length < 2) {
      return null
    }

    const first = recentHistory[0]
    const last = recentHistory[recentHistory.length - 1]
    const trend = last.used - first.used

    return {
      trend, // 正数表示增长，负数表示下降
      rate: trend / minutes, // MB/分钟
      samples: recentHistory.length
    }
  }

  /**
   * 获取监控报告
   */
  getReport () {
    const currentMemory = this.getMemoryInfo()
    const trend = this.getMemoryTrend()

    return {
      current: currentMemory,
      trend,
      history: this.memoryHistory.slice(-10), // 最近10条记录
      thresholds: {
        warning: this.options.warningThreshold,
        critical: this.options.criticalThreshold,
        max: this.options.maxThreshold
      },
      isMonitoring: this.isMonitoring
    }
  }
}

// 创建全局实例
const memoryMonitor = new MemoryMonitor()

export default memoryMonitor
