/**
 * 第三方库资源管理器
 * 统一管理xgplayer、wavesurfer、luckysheet等第三方库的生命周期
 */

class ResourceManager {
  constructor () {
    this.instances = new Map() // 存储所有实例
    this.timers = new Set() // 存储所有定时器
    this.eventListeners = new Map() // 存储事件监听器
    this.observers = new Set() // 存储观察者对象
  }

  /**
   * 创建并管理xgplayer实例
   */
  async createVideoPlayer (config, instanceId = 'default') {
    try {
      // 清理已存在的同名实例
      this.destroyVideoPlayer(instanceId)

      const Player = await import('xgplayer').then(res => res.default)
      let VideoPlayer = Player

      // 根据文件类型选择合适的播放器
      if (config.url) {
        const suffix = this.getUrlSuffix(config.url)
        if (suffix === 'mp4') {
          const Mp4Player = await import('xgplayer-mp4').then(res => res.default)
          VideoPlayer = Mp4Player
        } else if (suffix === 'm3u8') {
          const HlsJsPlayer = await import('xgplayer-hls.js').then(res => res.default)
          VideoPlayer = HlsJsPlayer
        } else if (suffix === 'flv') {
          const FlvJsPlayer = await import('xgplayer-flv.js').then(res => res.default)
          VideoPlayer = FlvJsPlayer
        }
      }

      const player = new VideoPlayer(config)

      // 注册实例
      this.registerInstance('video', instanceId, player)

      return player
    } catch (error) {
      console.error('创建视频播放器失败:', error)
      throw error
    }
  }

  /**
   * 销毁视频播放器
   */
  destroyVideoPlayer (instanceId = 'default') {
    const player = this.getInstance('video', instanceId)
    if (player) {
      try {
        if (player.destroy) {
          player.destroy()
        } else if (player.dispose) {
          player.dispose()
        }
        this.unregisterInstance('video', instanceId)
      } catch (error) {
        console.error('销毁视频播放器失败:', error)
      }
    }
  }

  /**
   * 创建并管理wavesurfer实例
   */
  async createAudioPlayer (config, instanceId = 'default') {
    try {
      // 清理已存在的同名实例
      this.destroyAudioPlayer(instanceId)

      const WaveSurfer = await import('wavesurfer.js').then(res => res.default)
      const wavesurfer = WaveSurfer.create(config)

      // 注册实例
      this.registerInstance('audio', instanceId, wavesurfer)

      return wavesurfer
    } catch (error) {
      console.error('创建音频播放器失败:', error)
      throw error
    }
  }

  /**
   * 销毁音频播放器
   */
  destroyAudioPlayer (instanceId = 'default') {
    const wavesurfer = this.getInstance('audio', instanceId)
    if (wavesurfer) {
      try {
        wavesurfer.destroy()
        this.unregisterInstance('audio', instanceId)
      } catch (error) {
        console.error('销毁音频播放器失败:', error)
      }
    }
  }

  /**
   * 创建并管理luckysheet实例
   */
  async createExcelViewer (config, instanceId = 'default') {
    try {
      // 清理已存在的同名实例
      this.destroyExcelViewer(instanceId)

      const LuckyExcel = await import('luckyexcel').then(res => res.default)

      return new Promise((resolve, reject) => {
        LuckyExcel.transformExcelToLuckyByUrl(
          config.url,
          config.name,
          (exportJson, luckysheetfile) => {
            try {
              // 销毁现有的luckysheet实例
              if (window.luckysheet) {
                window.luckysheet.destroy()
              }

              const luckysheetConfig = {
                data: exportJson.sheets,
                title: exportJson.info.name,
                userInfo: exportJson.info.name.creator,
                hook: {
                  // 添加销毁钩子
                  beforeDestroy: () => {
                    this.unregisterInstance('excel', instanceId)
                  }
                },
                ...config.options
              }

              window.luckysheet.create(luckysheetConfig)

              // 注册实例（这里注册的是配置对象，因为luckysheet是全局单例）
              this.registerInstance('excel', instanceId, {
                config: luckysheetConfig,
                destroy: () => {
                  if (window.luckysheet) {
                    window.luckysheet.destroy()
                  }
                }
              })

              resolve(window.luckysheet)
            } catch (error) {
              reject(error)
            }
          }
        )
      })
    } catch (error) {
      console.error('创建Excel查看器失败:', error)
      throw error
    }
  }

  /**
   * 销毁Excel查看器
   */
  destroyExcelViewer (instanceId = 'default') {
    const instance = this.getInstance('excel', instanceId)
    if (instance) {
      try {
        instance.destroy()
        this.unregisterInstance('excel', instanceId)
      } catch (error) {
        console.error('销毁Excel查看器失败:', error)
      }
    }
  }

  /**
   * 注册实例
   */
  registerInstance (type, id, instance) {
    const key = `${type}_${id}`
    this.instances.set(key, instance)
  }

  /**
   * 获取实例
   */
  getInstance (type, id) {
    const key = `${type}_${id}`
    return this.instances.get(key)
  }

  /**
   * 注销实例
   */
  unregisterInstance (type, id) {
    const key = `${type}_${id}`
    this.instances.delete(key)
  }

  /**
   * 注册定时器
   */
  registerTimer (timer) {
    this.timers.add(timer)
    return timer
  }

  /**
   * 清理定时器
   */
  clearTimer (timer) {
    if (this.timers.has(timer)) {
      clearTimeout(timer)
      clearInterval(timer)
      this.timers.delete(timer)
    }
  }

  /**
   * 注册事件监听器
   */
  registerEventListener (element, event, handler, options) {
    const key = `${element}_${event}`
    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, [])
    }
    this.eventListeners.get(key).push({ handler, options })
    element.addEventListener(event, handler, options)
  }

  /**
   * 清理事件监听器
   */
  clearEventListener (element, event) {
    const key = `${element}_${event}`
    const listeners = this.eventListeners.get(key)
    if (listeners) {
      listeners.forEach(({ handler, options }) => {
        element.removeEventListener(event, handler, options)
      })
      this.eventListeners.delete(key)
    }
  }

  /**
   * 注册观察者
   */
  registerObserver (observer) {
    this.observers.add(observer)
    return observer
  }

  /**
   * 清理观察者
   */
  clearObserver (observer) {
    if (this.observers.has(observer)) {
      if (observer.disconnect) {
        observer.disconnect()
      } else if (observer.unobserve) {
        observer.unobserve()
      }
      this.observers.delete(observer)
    }
  }

  /**
   * 获取文件后缀名
   */
  getUrlSuffix (url) {
    const arr = url.split('.')
    return arr[arr.length - 1].toLowerCase()
  }

  /**
   * 清理所有资源
   */
  destroyAll () {
    // 清理所有实例
    this.instances.forEach((instance, key) => {
      try {
        if (instance.destroy) {
          instance.destroy()
        } else if (instance.dispose) {
          instance.dispose()
        } else if (instance.close) {
          instance.close()
        }
      } catch (error) {
        console.error(`清理实例 ${key} 失败:`, error)
      }
    })
    this.instances.clear()

    // 清理所有定时器
    this.timers.forEach(timer => {
      clearTimeout(timer)
      clearInterval(timer)
    })
    this.timers.clear()

    // 清理所有事件监听器
    this.eventListeners.clear()

    // 清理所有观察者
    this.observers.forEach(observer => {
      try {
        if (observer.disconnect) {
          observer.disconnect()
        } else if (observer.unobserve) {
          observer.unobserve()
        }
      } catch (error) {
        console.error('清理观察者失败:', error)
      }
    })
    this.observers.clear()
  }

  /**
   * 获取资源使用情况
   */
  getResourceStats () {
    return {
      instances: this.instances.size,
      timers: this.timers.size,
      eventListeners: this.eventListeners.size,
      observers: this.observers.size
    }
  }
}

// 创建全局单例
const resourceManager = new ResourceManager()

export default resourceManager
