import store from '@/store'
import Vue from 'vue'
function insertJs (src) {
  return new Promise((resolve, reject) => {
    for (let i = 0; i < document.scripts.length; i++) {
      if (document.scripts[i].src === src) {
        return resolve()
      }
    }
    var script = document.createElement('script')
    script.onload = resolve
    script.src = src
    document.body.appendChild(script)
  })
}
// 获取上传client
async function getOSSClient ({ isPrivate } = { isPrivate: false }) {
  if (!getOSSClient.hasSDK) {
    // 加载js文件
    await insertJs('https://gosspublic.alicdn.com/aliyun-oss-sdk-6.16.0.min.js')
    getOSSClient.hasSDK = true
  }

  const refreshSTSTokenInterval = 300000
  const API = this.$api.fileFetchUploadToken
  const data = await API(refreshSTSTokenInterval)
  getOSSClient.resourcePrefix = data.resourcePrefix
  const ossClient = new window.OSS(
    {
      endpoint: data.domain,
      cname: true,
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
      region: data.region,
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: data.accessKeyId,
      accessKeySecret: data.accessKeySecret,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: data.securityToken,
      refreshSTSTokenInterval: refreshSTSTokenInterval - 1000,
      // 填写Bucket名称。
      bucket: isPrivate ? data.privateBucketName : data.bucketName
    }
  )
  return ossClient
}

// 上传大文件
async function uploadBigFile (file, config) {
  const ossClient = await getOSSClient.call(this, config)
  // store.commit('addTask', { name: `文件上传：${file.name}`, uid: file.uid, progress: 0 })
  const result = await ossClient.multipartUpload(`${getOSSClient.resourcePrefix}/${file.uid}-${file.name}`, file, {
    headers: {
      'x-oss-object-acl': config.isPrivate ? 'private' : 'public-read'
    },
    progress: (p, cpt, res) => {
      if (config.onProgress) {
        config.onProgress(Math.floor(p * 100))
      }

      // store.commit('updateTaskProgress', { uid: file.uid, progress: Math.floor(p * 100) })
    }
  })
  return result
}

// 水印文件下载
function downloadWatermarkFile (url, fileName, params = { rowSpace: 200, colSpace: 200, angle: 0.35, color: '153,153,153', fontSize: 23 }) {
  const encode = (str) => encodeURIComponent(this.$utils.Base64.encode(`${str}`))
  let paramStr = ''
  for (const key in params) {
    paramStr += `&${key}=${encodeURIComponent(params[key])}`
  }
  const name = window.QIANKUN_DATA.user_info.user.name
  const urlName = fileName || this.$utils.getFileName(url)
  const fileType = urlName.split('.')[1]
  if (fileType === 'pdf') {
    url = `${window.QIANKUN_SETTING.BASE_URL}/file/api/file/downloadWatermarkFile?url=${encode(url)}&waterMark=${encodeURIComponent(`HERMES:${name}`)}&${paramStr}`
  }
  this.$utils.funDownload(url, urlName)
}

export default {
  getOSSClient,
  uploadBigFile,
  downloadWatermarkFile
}
