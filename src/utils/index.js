// 获取菜单信息Id等合并到tab中
function getUserMenuInfo (routePath, tenantId) {
  if (tenantId && window.QIANKUN_DATA.active !== tenantId) {
    return null
  }
  // 静态匹配
  let item = window.QIANKUN_DATA.user_info.menus.find(it => it.href && it.href.replace(/^\//, '') === routePath)
  // 动态路由匹配
  if (!item) {
    routePath = routePath.replace(/^\//, '')
    item = window.QIANKUN_DATA.user_info.menus.find(it => {
      const regex = convertToRegex(it.href.replace(/^\//, ''))
      return it.href && regex.test(routePath)
    })
  }
  return item
}

function convertToRegex (urlPattern) {
  // 将/:XXX形式的参数替换为正则表达式的捕获组 ([^/]+)
  // 这里假设参数不包含斜杠
  const regexPattern = urlPattern.replace(/\/:[^/]+/g, '/([^/]+)')
  // 创建正则表达式对象，添加^和$来匹配字符串的开始和结束
  const regex = new RegExp(`^${regexPattern}$`)
  return regex
}

// 关闭标签页以后的路由处理,如果关闭的非本路由标签，不跳转
function afterCloseTabHandleRoute (route, router) {
  const { path, hash } = route
  const tabList = window.QIANKUN_STORE.state.pageNav
  const item = tabList.find(it => it.pathname.replace(/^\//, '') === path.replace(/^\//, '') && hash.replace('#', '').replace(/\?.*$/, '') === it.path)
  if (!item) {
    if (tabList.length === 0) {
      router.push('/')
    } else { // 关闭页面后跳转到同租户路由，如果没有同租户路由，跳转到最后一个路由
      const index = tabList.findLastIndex(it => it.tenantId === window.QIANKUN_DATA.active)
      const item = tabList[index] || tabList[tabList.length - 1]
      router.push(`${item.pathname}#${item.fullPath || ''}`)
    }
  }
}

// 等待拥有后执行,限次数，控制时间每次叠加
function untilRunFn (isOkFn, fn, t = 1, count = 6) {
  if (count <= 0) {
    return false
  }
  if (isOkFn()) {
    fn()
  } else {
    setTimeout(() => {
      untilRunFn(isOkFn, fn, t * 5, --count)
    }, t)
  }
}

// 返回一个函数，一分钟内不会重复执行
function throttle (fn, wait) {
  let lastTime = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastTime > wait) {
      fn.apply(this, args)
      lastTime = now
    }
  }
}

// 根据租户id获取appName
function getAppnameByTenantId (tenantId) {
  if (tenantId === 'hermes-workspace') {
    return '工作台'
  }
  const topJSON = window.QIANKUN_DATA.topsJson
  for (let i = 0; i < topJSON.length; i++) {
    const item = topJSON[i]
    if (item.value === tenantId) {
      return item.label
    }
    if (item.child) {
      for (let j = 0; j < item.child.length; j++) {
        const child = item.child[j]
        if (child.value === tenantId) {
          return child.label
        }
      }
    }
  }
}

function dealUrl (url) {
  if (!url) {
    return ''
  }
  if (url.startsWith('/_blank_')) {
    return url
  }
  if (url.startsWith(`/${window.QIANKUN_DATA.localPathname}#/`)) {
    return url
  }
  return `/${window.QIANKUN_DATA.localPathname}#/${url.replace(/^\//, '')}`
}

// 菜单url
function goMenuUrl (index) {
  if (!index) {
    return
  }
  if (/^\/_blank_/.test(index)) {
    window.open(index.slice(8))
    return false
  }
  this.$router.push(dealUrl(`${index}`))
}

// 跨系统url
function goSystemUrl (pathname, fullPath) {
  pathname = pathname.startsWith('/') ? pathname : '/' + pathname
  fullPath = fullPath.startsWith('/') ? fullPath : '/' + fullPath
  this.$router.push(`${pathname}#${fullPath}`)
}

export default {
  goSystemUrl,
  dealUrl,
  goMenuUrl,
  throttle,
  untilRunFn,
  getUserMenuInfo,
  afterCloseTabHandleRoute,
  getAppnameByTenantId
}
