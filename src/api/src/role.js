import Vue from 'vue'
export default {
  // 获取角色
  getUserRoleList (params) {
    return Vue.prototype.$utils.axios.post('/user/api/role/list', params)
  },
  // 创建角色
  createUserRole (params) {
    return Vue.prototype.$utils.axios.post('/user/api/role/create', params)
  },
  // 创建角色
  updateUserRole (params) {
    return Vue.prototype.$utils.axios.post('/user/api/role/update', params)
  },
  // 删除角色
  delUserRole (params) {
    return Vue.prototype.$utils.axios.post('/user/api/role/delete', null, { params })
  },
  // 获取选择角色菜单
  getRoleMenu (params) {
    return Vue.prototype.$utils.axios.get('/user/api/role/menuAssignList', { params })
  },
  // 获取菜单
  getMenuList () {
    return Vue.prototype.$utils.axios.post('/user/api/menu/listAll', { tenantId: window.QIANKUN_SETTING.TENANT_ID })
  },
  // 设置菜单
  setRoleMenu (params) {
    return Vue.prototype.$utils.axios.post('/user/api/role/assignRoleResource', params)
  }
}
