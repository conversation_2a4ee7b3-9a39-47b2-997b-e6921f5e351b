import Vue from 'vue'
export default {
  // 获取角色
  getUserList (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/listUserPageByCondition', params)
  },
  // 创建角色
  createUser (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/createByAdmin', params)
  },
  // 创建角色
  updateUser (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/updateByAdmin', params)
  },
  // 删除角色
  delUser (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/delete', params)
  },
  // 删除角色
  getUser (params) {
    return Vue.prototype.$utils.axios.get('/user/api/user/getUserForAdmin', { params })
  },
  // 修改密码
  userModifyPwd (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/modifyPwd', null, {
      params,
      headers: {
        'X-Session-Id': window.QIANKUN_DATA.userToken.sessionId
      }
    })
  },
  //  负责人信息
  userListAll (params) {
    return Vue.prototype.$utils.axios.post('/user/api/user/listAll', params)
  }
}
