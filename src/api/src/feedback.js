import Vue from 'vue'
export default {
  // 获取角色
  feedbackCreate (data) {
    return Vue.prototype.$utils.axios.post('/tool/feedback/create', {
      ...data,
      name: window.QIANKUN_DATA.user_info.user.name,
      system: window.QIANKUN_DATA.active
    })
  },
  greaState () {
    return Vue.prototype.$utils.axios.post('/tool/feedback/get/great/state', {
      great: 1,
      name: '田木',
      system: window.QIANKUN_DATA.active
    })
  }
}
