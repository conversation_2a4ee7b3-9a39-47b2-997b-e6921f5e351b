import Vue from 'vue'
export default {
  // 获取收藏夹数据列表
  async getTabList (params) {
    const data = await Vue.prototype.$utils.axios.get('/user/api/tab/listHermesTabByLoginName', { params })

    data.forEach(it => {
      const href = window.QIANKUN_DATA.hostMap[it.tenantId]
      if (!href) {
        return
      }
      const match = href.match(/\/([^\\/]+)\/?$/)
      const pathname = match ? `/${match[1]}` : null
      it.pathname = pathname
      it.qiankunKey = `${it.tenantId}${it.code}+${it.href}` // 为了兼容子系统code码相同的情况
    })
    return data.filter(it => it.qiankunKey)
  },
  createTab (data) {
    return Vue.prototype.$utils.axios.post('/user/api/tab/createHermesTab', data)
  },
  // 创建角色
  delTab (data) {
    return Vue.prototype.$utils.axios.post('/user/api/tab/deleteHermesTab', data)
  },
  // 排序
  updateBySortTab (data) {
    return Vue.prototype.$utils.axios.post('/user-service/api/tab/updateBySort', data)
  }
}
