import Vue from 'vue'
// api缓存
const apiCache = {}
export default {
  login (params) {
    return Vue.prototype.$utils.axios.post('/user/login', params)
  },
  // 登出
  logout () {
    return Vue.prototype.$utils.axios.post('/user/logout')
  },
  // 获取所有业务字典
  async getdataDictionary (tenantId) {
    if (!apiCache[tenantId]) {
      apiCache[tenantId] = await Vue.prototype.$utils.axios.post('/common/api/dict/listAll', { tenantId })
    }
    return apiCache[tenantId]
  },
  //  获取用户信息
  async userVo (token) {
    const key = `userVo${JSON.stringify({
      token,
      tenantId: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
      extTenantId: window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id']
    })}`
    if (!apiCache[key]) {
      apiCache[key] = await Vue.prototype.$utils.axios.post('/user/api/user/getVoHiddenMenu', {
        superAdmin: false,
        tenantId: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
        extTenantId: window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id']
      }, {
        headers: {
          'X-Session-Id': token.sessionId
        }
      })
      // 设置头像
      // if (apiCache[key].user?.loginName) {
      //   const userInfo = await Vue.prototype.$utils.axios.get('/employee-center-service/api/employeeCenter/employeeData/getByLoginName', {
      //     params: {
      //       loginName: apiCache[key].user.loginName
      //     }
      //   })
      //   apiCache[key].user.photo = userInfo.avatarUrl
      // }
    }
    return apiCache[key]
  },
  // 获取用户飞书头像
  getFeishuUserHeadr () {
    return Vue.prototype.$utils.axios.get('/employee-center-service//api/employeeCenter/employeeAdmin/getByUserSession', {
      headers: {
        'X-Tenant-Id': 'HERMES'
      }
    })
  },
  // 文件上传
  fileUpload (params) {
    return Vue.prototype.$utils.axios.post('/file/api/file/upload', params)
  },
  // 批量埋点
  batchReport (events) {
    return Vue.prototype.$utils.axios.post('/tracker-service/event/batchReport', events)
  },
  // 获取映射域名租户关系列表
  toolAppListAll () {
    return Vue.prototype.$utils.axios.post('/common/api/app/listAll', {
      labelQuery: 'hermes'
    })
  },
  // 获取拥有权限
  getTenantIdsByVirtualTenantId (params) {
    const key = 'getTenantIdsByLoginName'
    if (!apiCache[key]) {
      apiCache[key] = Vue.prototype.$utils.axios.get('/user/api/user/getTenantIdsByLoginName', {
        headers: {
          'X-Tenant-Id': 'HERMES'
        }
      })
    }
    return apiCache[key]
  },
  // 获取系统session
  async createUserSession (headers) {
    const key = `createUserSession${JSON.stringify({
        tenantId: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
        extTenantId: window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id']
    })}`
    if (!apiCache[key]) {
      apiCache[key] = await Vue.prototype.$utils.axios.get('/user/api/user/createUserSession', {
        headers: {
          ...headers
        }
      })
    }
    return apiCache[key]
  },
  // 根据花名名获取用户信息
  getPlatformUserByLoginName (loginName) {
    return Vue.prototype.$utils.axios.get('/employee-center-service/api/employeeCenter/employeeData/getByLoginName', {
      params: {
        loginName
      }
    })
  },
  // 根据花名名获取OpenId
  getEmployeeDataFeishuInfoByName (name) {
    return Vue.prototype.$utils.axios.get('/employee-center-service/api/employeeCenter/employeeData/getFeishuInfoByName', {
      params: {
        name
      }
    })
  },
  // 根据中文名获取花名
  getMasterOrgListUserAccount (name) {
    return Vue.prototype.$utils.axios.get('/dingtalk//api/master/org/list/user/account', {
      params: {
        name
      }
    })
  },
  // 拉用户进办公平台群组
  dingtalkAddPlatformChat (addLoginNameList, chatid, appId) {
    return Vue.prototype.$utils.axios.post('/dingtalk//api/master/org/addPlatformChat', {
      addLoginNameList: addLoginNameList,
      chatid
    }, {
      params: {
        appId
      }
    })
  },
  // 接口获取top.json
  getTopJson (name) {
    return Vue.prototype.$utils.axios.get('/app-service/api/hermesTopData/topJson', {
      params: {
        name
      }
    })
  }
}
