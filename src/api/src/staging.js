import Vue from 'vue'
export default {
  // 获取我的收藏夹
  listFavoriteModuleByUser (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/home/<USER>', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 获取我的资讯
  listNewsByUser (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service//api/home/<USER>', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 获取我的待办
  listTodoByUser (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service//api/home/<USER>', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 获取我的常用功能
  listTopUsedModuleByUser (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service//api/home/<USER>', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  }
}
