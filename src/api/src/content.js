import Vue from 'vue'
export default {
  // 获取资讯分页列表
  hermesWorkspaceNewsListPage (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/listPage', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 新增资讯
  hermesWorkspaceNewsCreate (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/create', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 上架
  hermesWorkspaceNewsPublish (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/publish?id=' + params.id, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 下架
  hermesWorkspaceNewsCancelPublish (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/cancelPublish?id=' + params.id, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 删除
  hermesWorkspaceNewsDelete (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/delete?id=' + params.id, undefined, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 获取资讯信息
  hermesWorkspaceNewsGet (params) {
    return Vue.prototype.$utils.axios.get('/hermes-workspace-service/api/news/get', { params }, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 修改资讯信息
  hermesWorkspaceNewsUpdate (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/update', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 保存草稿资讯信息
  hermesWorkspaceNewsSubmitDraft (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/submitDraft', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 批量上架
  hermesWorkspaceNewsPublishByIds (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/publishByIds', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 批量下架
  hermesWorkspaceNewsCancelPublishByIds (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/cancelPublishByIds', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  },
  // 批量删除
  hermesWorkspaceNewsDeleteByIds (params) {
    return Vue.prototype.$utils.axios.post('/hermes-workspace-service/api/news/deleteByIds', params, {
      headers: {
        'X-Session-Id': window.QIANKUN_DATA?.userToken?.sessionId
      }
    })
  }
}
