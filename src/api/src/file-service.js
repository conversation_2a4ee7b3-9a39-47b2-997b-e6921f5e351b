import Vue from 'vue'
export default {
  // 创建菜单
  fileFetchUploadToken (refreshSTSTokenInterval) {
    return Vue.prototype.$utils.axios({ method: 'post', url: '/file-service/api/file/fetchUploadToken', data: { durationSeconds: refreshSTSTokenInterval / 1000 } })
  },
  fileFetchGetSignedUrl (filePath, expire) {
    const uri = new URL(filePath)
    return Vue.prototype.$utils.axios({ method: 'post', url: '/file-service/api/file/getSignedUrl', params: { filePath: uri.pathname.slice(1), expire } })
  }
}
