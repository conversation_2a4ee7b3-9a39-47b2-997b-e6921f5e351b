import axios from 'axios'
import ElementUI from 'element-ui'
const { Message } = ElementUI
// 动态引用api
const files = require.context('@/api/src', false, /\.js$/)
const modules = {}
files.keys().forEach(key => {
  Object.assign(modules, files(key).default || files(key))
})

export default {
  install (Vue) {
    const { loading, getCookie } = Vue.prototype.$utils
    const instance = axios.create({
      baseURL: window.QIANKUN_SETTING.BASE_URL,
      timeout: 100000,
      headers: {
        'Content-Type': 'application/json',
        'X-Session-Id': getCookie(`${location.hostname}.mushroom.session.id`)
      }
    })
    instance.interceptors.request.use(
      config => {
        // loading.open()
        config.headers = {
          ...config.headers,
          ...window.QIANKUN_DATA.apiHeader
        }
        return Promise.resolve(config)
      },
      error => {
        return Promise.reject(error)
      }
    )
    instance.interceptors.response.use(
      (response) => {
        // loading.close()
        if (response.data.code === undefined) {
          return Promise.resolve(response.data)
        } else if (response.data.code === '400') {
          if (window.isRes400) {
            return false
          }
          Vue.prototype.$alert('登录过期请重新登录', '警告', {
            confirmButtonText: '确定',
            callback: action => {
              Vue.prototype.$goLogin()
            }
          })
          window.isRes400 = true
          return false
        } else if (response.data.code === '800') {
          Message.error('服务器错误')
          return Promise.reject(response)
        } else if (response.data.code === '600') {
          Message.error('服务器错误')
          return Promise.reject(response)
        } else if (response.data.code === '700') {
          Message.error(response.data.msg)
          return Promise.reject(response)
        } else if (response.data.code === '403') {
          Message.error(response.data.msg)
          return Promise.reject(response)
        } else if (response.data.code === '401') {
          return Promise.reject(response)
        } else if (response.data.code !== '0') {
          Message.error(response.data.msg)
          return Promise.reject(response)
        } else {
          return Promise.resolve(response.data.data)
        }
      },
      (error) => {
        // loading.close()
        if (error.response.data.status === 404 || error.response.data.code === 404 || error.response.data.code === '404') {
          Message.error('接口路径错误')
        } else if (error.response.data.status === 500 || error.response.data.code === 500 || error.response.data.code === '500') {
          Message.error('系统开了个小差，请稍后再试')
        }
        return Promise.reject(error)
      }
    )
    // 插件中加入API
    Vue.prototype.$utils = { ...Vue.prototype.$utils, axios: instance, api: modules }
    Vue.prototype.$api = modules
  }
}
