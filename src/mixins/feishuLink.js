export default {
  methods: {
    // 飞书一对一聊天
    async goFeishu<PERSON><PERSON> (name) {
      // const loginName = await this.$api.getMasterOrgListUserAccount(name).then(d => d[0].loginName) // 通过中文名获取拼音
      // const openUserId = await this.$api.getPlatformUserByLoginName(loginName).then(d => d.feishuOpenUserId) // 获取userId
      const openUserId = await this.$api.getEmployeeDataFeishuInfoByName(name).then(d => d.feishuOpenUserId) // 获取userId
      window.open(`https://applink.feishu.cn/client/chat/open?openId=${openUserId}`)
      // this.goFeishuRobotChat('oc_ba7ad247b42fcef016661b6d1814b156', 'cli_9f79032e2834100e')
    },
    // 创建群组聊天窗口
    async gotFeishuGroupChat (name, loginNamelist) {

    },
    // 将当前登录用户拉入飞书群中
    async goFeishuRobotChat (chatid, appId) {
      // const chatid = 'oc_ba7ad247b42fcef016661b6d1814b156'
      // const appId = 'cli_9f79032e2834100e'
      const loginName = window.QIANKUN_DATA.user_info.user.loginName // 通过中文名获取拼音
      await this.$api.dingtalkAddPlatformChat([loginName], chatid, appId)
      window.open(`https://applink.feishu.cn/client/chat/open?openChatId=${chatid}`)
    }
  }
}
