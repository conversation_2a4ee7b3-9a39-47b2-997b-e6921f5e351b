
export default {
  data () {
    return {
      isNotebook: false
    }
  },
  watch: {
    '$route' () {
      this.initReSize()
    }
  },
  methods: {
    isSmall () {
      const isNotebook = document.body.clientWidth < 1601
      window.QIANKUN_DATA.isNotebook = isNotebook
      return isNotebook
    },
    initList () {
      window.addEventListener('resize', () => {
        this.initReSize()
      })
    },
    initReSize () {
      this.setNotebookModel()
      this.setNotebookMenu()
    },
    setNotebookModel () {
      const isSmall = this.isSmall()
      const localPathname = window.QIANKUN_DATA.localPathname
      this.isNotebook = isSmall
      if (this.isSmall()) {
        document.body.classList.add('is-notebook')
        document.body.classList.add(localPathname)
      } else {
        document.body.classList.remove('is-notebook')
        document.body.classList.remove(localPathname)
      }
    },
    setNotebookMenu () {
      // const isSmall = this.isSmall()
      // const { localPathname } = window.QIANKUN_DATA
      // if (['commodity'].indexOf(localPathname) !== -1) {
      //   if (isSmall) {
      //     this.collapse = true
      //   }
      // }
    }
  },
  created () {
    this.initList()
    this.initReSize()
  }
}
