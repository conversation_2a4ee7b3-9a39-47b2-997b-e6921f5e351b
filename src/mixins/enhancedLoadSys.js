/**
 * 增强版子应用加载和销毁管理
 * 解决内存泄漏和性能问题
 */

export default {
  data () {
    return {
      micApp: {},
      appVM: {},
      appDomMap: {},
      childVm: null,
      systemLoading: false,
      // 新增：资源清理追踪
      resourceTracker: {
        timers: new Set(),
        eventListeners: new Map(),
        thirdPartyInstances: new Map(),
        observers: new Set()
      }
    }
  },

  methods: {
    /**
     * 增强版子应用销毁方法
     */
    async destroyChildVm (appName) {
      console.log(`开始销毁子应用: ${appName}`)

      try {
        // 1. 销毁qiankun微应用
        const micApp = this.micApp[appName]
        if (micApp) {
          if (micApp.getStatus() === 'MOUNTED') {
            await micApp.unmount()
          }
          delete this.micApp[appName]
        }

        // 2. 清理Vue实例
        const childVm = this.appVM[appName]
        if (childVm) {
          await this.cleanupVueInstance(childVm, appName)
          delete this.appVM[appName]
        }

        // 3. 清理DOM引用
        if (this.appDomMap[appName]) {
          this.cleanupDOMReferences(this.appDomMap[appName])
          delete this.appDomMap[appName]
        }

        // 4. 清理应用相关的全局资源
        this.cleanupGlobalResources(appName)

        console.log(`子应用 ${appName} 销毁完成`)
      } catch (error) {
        console.error(`销毁子应用 ${appName} 时出错:`, error)
      }
    },

    /**
     * 清理Vue实例
     */
    async cleanupVueInstance (childVm, appName) {
      if (!childVm) return

      try {
        // 清理路由钩子
        if (childVm.$router) {
          childVm.$router.beforeHooks = []
          childVm.$router.afterHooks = []
          childVm.$router.resolveHooks = []

          // 清理路由历史记录
          if (childVm.$router.history) {
            childVm.$router.history.teardown()
          }
        }

        // 清理Vuex store
        if (childVm.$store) {
          // 清理store的订阅
          if (childVm.$store._subscribers) {
            childVm.$store._subscribers = []
          }
          if (childVm.$store._actionSubscribers) {
            childVm.$store._actionSubscribers = []
          }
        }

        // 清理组件树中的事件监听器
        this.cleanupComponentEventListeners(childVm)

        // 销毁Vue实例
        childVm.$destroy()

        // 清理原型链注入的属性
        const desList = ['$api', '$utils', '$dict', '$preview', '$upEvent']
        desList.forEach(key => {
          // eslint-disable-next-line no-prototype-builtins
          if (childVm.constructor.prototype.hasOwnProperty(key)) {
            delete childVm.constructor.prototype[key]
          }
        })
      } catch (error) {
        console.error('清理Vue实例时出错:', error)
      }
    },

    /**
     * 清理组件事件监听器
     */
    cleanupComponentEventListeners (vm) {
      if (!vm) return

      // 递归清理子组件
      if (vm.$children && vm.$children.length) {
        vm.$children.forEach(child => {
          this.cleanupComponentEventListeners(child)
        })
      }

      // 清理组件的事件监听器
      if (vm._events) {
        Object.keys(vm._events).forEach(eventName => {
          vm.$off(eventName)
        })
      }

      // 清理DOM事件监听器
      if (vm.$el && vm.$el.removeEventListener) {
        // 这里需要根据具体应用的事件监听器进行清理
        const commonEvents = ['click', 'scroll', 'resize', 'keydown', 'keyup']
        commonEvents.forEach(eventType => {
          // 注意：这里只能清理通过addEventListener添加的监听器
          // 需要在添加监听器时保存引用才能正确清理
        })
      }
    },

    /**
     * 清理DOM引用
     */
    cleanupDOMReferences (domElement) {
      if (!domElement) return

      try {
        // 清理DOM元素的事件监听器
        if (domElement.cloneNode) {
          const cleanElement = domElement.cloneNode(false)
          if (domElement.parentNode) {
            domElement.parentNode.replaceChild(cleanElement, domElement)
          }
        }

        // 清理可能的循环引用
        if (domElement._vue) {
          domElement._vue = null
        }
      } catch (error) {
        console.error('清理DOM引用时出错:', error)
      }
    },

    /**
     * 清理全局资源
     */
    cleanupGlobalResources (appName) {
      // 清理定时器
      this.resourceTracker.timers.forEach(timerId => {
        clearTimeout(timerId)
        clearInterval(timerId)
      })
      this.resourceTracker.timers.clear()

      // 清理第三方库实例
      const instances = this.resourceTracker.thirdPartyInstances.get(appName)
      if (instances) {
        instances.forEach(instance => {
          try {
            if (instance.destroy) {
              instance.destroy()
            } else if (instance.dispose) {
              instance.dispose()
            } else if (instance.close) {
              instance.close()
            }
          } catch (error) {
            console.error('清理第三方库实例时出错:', error)
          }
        })
        this.resourceTracker.thirdPartyInstances.delete(appName)
      }

      // 清理观察者
      this.resourceTracker.observers.forEach(observer => {
        try {
          if (observer.disconnect) {
            observer.disconnect()
          } else if (observer.unobserve) {
            observer.unobserve()
          }
        } catch (error) {
          console.error('清理观察者时出错:', error)
        }
      })
      this.resourceTracker.observers.clear()
    },

    /**
     * 注册定时器（用于追踪和清理）
     */
    registerTimer (timerId) {
      this.resourceTracker.timers.add(timerId)
      return timerId
    },

    /**
     * 注册第三方库实例（用于追踪和清理）
     */
    registerThirdPartyInstance (appName, instance) {
      if (!this.resourceTracker.thirdPartyInstances.has(appName)) {
        this.resourceTracker.thirdPartyInstances.set(appName, new Set())
      }
      this.resourceTracker.thirdPartyInstances.get(appName).add(instance)
    },

    /**
     * 增强版等待函数，支持超时和清理
     */
    awaitUtilFn (fn, count = 30, time = 300) {
      return new Promise((resolve, reject) => {
        if (fn()) {
          resolve()
          return
        }

        let currentCount = count
        const timer = setInterval(() => {
          currentCount--
          if (currentCount <= 0) {
            clearInterval(timer)
            this.resourceTracker.timers.delete(timer)
            reject(new Error('等待超时'))
            return
          }

          if (fn()) {
            clearInterval(timer)
            this.resourceTracker.timers.delete(timer)
            resolve()
          }
        }, time)

        this.registerTimer(timer)
      })
    },

    /**
     * 内存使用情况检查
     */
    checkMemoryUsage () {
      if (window.performance && window.performance.memory) {
        const memory = window.performance.memory
        return {
          used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
          total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
          limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
        }
      }
      return null
    },

    /**
     * 强制垃圾回收（仅在开发环境）
     */
    forceGarbageCollection () {
      if (window.gc && process.env.NODE_ENV === 'development') {
        window.gc()
      }
    }
  },

  beforeDestroy () {
    // 清理所有资源
    Object.keys(this.micApp).forEach(appName => {
      this.destroyChildVm(appName)
    })
  }
}
