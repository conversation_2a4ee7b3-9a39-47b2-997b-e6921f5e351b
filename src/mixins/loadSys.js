import {
  start,
  loadMicroApp,
  initGlobalState,
  registerMicroApps,
  prefetchApps,
  addGlobalUncaughtErrorHandler
} from 'qiankun'

export default {
  data () {
    return {
      micApp: {}, // 微应用实例
      appDomMap: {}, // 微应用实例Dom
      appVM: {} // 微应用vue实例Vm
    }
  },
  watch: {

  },
  mounted () {
    addGlobalUncaughtErrorHandler(this.handerQiankunError)
  },
  methods: {
    // 微前端错误处理
    handerQiankunError () {
      this.systemLoading = false
    },
    addAppVm () {
      const name = window.QIANKUN_DATA.appConfig.activeRule
      this.appDomMap[name] = this.$refs.appRoot.children[0]
      this.appVM[name] = this.childVm
      const $router = this.childVm.$router
      // 当前子系统内部切换执行时执行，第一次不会执行
      $router.beforeEach(async (to, from, next) => {
        // await this.utilLoading()
        // console.log('beforeEach=====================================', name)
        if (name !== `/${window.QIANKUN_DATA.localPathname}`) { // 阻止子系统跳转逻辑
          return
        }
        this.callFn(this.childVm, 'onHide')
        next()
      })
      // 子路由进入后，执行onHide
      $router.afterEach((to) => {
        if (name !== `/${window.QIANKUN_DATA.localPathname}`) { // 阻止子系统跳转逻辑
          return
        }
        this.callFn(this.childVm, 'onShow')
      })
      // const transitionTo = $router.history.transitionTo
      // $router.history.transitionTo = function (...args) {
      //   if (name !== `/${window.QIANKUN_DATA.localPathname}`) { // 阻止子系统跳转逻辑
      //     return
      //   }
      //   transitionTo.call($router.history, ...args)
      // }
    },
    // 等到systemLoading为false的时候，再去加载子项目
    utilLoading () {
      return new Promise((resolve, reject) => {
        const timer = setInterval(() => {
          if (!this.systemLoading) {
            clearInterval(timer)
            resolve()
          }
        }, 200)
      })
    },
    async destroyChild () {
      const tenantIds = new Set(this.pageNav.map(it => it.pathname))
      for (const appName in this.micApp) {
        if (!tenantIds.has(appName)) {
          await this.destroyChildVm(appName)
          delete this.appDomMap[appName]
        }
      }
    },
    sleep (t) {
      return new Promise((resolve) => setTimeout(resolve, t))
    },
    async destroyChildVm (appName) {
      const micApp = this.micApp[appName]
      delete this.micApp[appName]
      if (micApp && micApp.getStatus() === 'MOUNTED') {
        await micApp.unmount()
      }
      const childVm = this.appVM[appName]
      if (childVm) {
        // vue路由摧毁
        // if (childVm.$router) {
        //   childVm.$router.beforeHooks = []
        //   childVm.$router.afterHooks = []
        //   childVm.$router.options.routes = []
        //   childVm.$router.afterHooks = []
        // }
        childVm.$destroy()
        // for (const key in childVm) {
        //   if (Object.prototype.hasOwnProperty.call(childVm, key)) {
        //     delete childVm[key]
        //   }
        // }
        // 删除原型链上的属性，注入的属性
        const desList = ['$api', '$utils', '$dict']
        for (const key in desList) {
          if (Object.prototype.hasOwnProperty.call(childVm.constructor.prototype, key)) {
            delete childVm.constructor.prototype[key]
          }
        }
        delete this.appVM[appName]
      }
    },
    async firstLoadApp () {
      const app = loadMicroApp(window.QIANKUN_DATA.appConfig, {
        sandbox: true,
        singular: false
      })
      this.micApp[window.QIANKUN_DATA.appConfig.activeRule] = app
      await app.mountPromise
      if (!this.$children.includes(window.__QIANKUN_SUB_APP_VM__)) {
        this.$children.push(window.__QIANKUN_SUB_APP_VM__)
      }
      app.update({ run: this.insertJs })
    },
    async runChildApp (to, from) {
      // const appNamePath = window.QIANKUN_DATA.appConfig.activeRule
      await this.destroyChild()
      const appDom = this.appDomMap[window.QIANKUN_DATA.appConfig.activeRule]
      if (!this.micApp[window.QIANKUN_DATA.appConfig.activeRule] || !appDom) {
        await this.firstLoadApp()
      } else {
        this.$refs.appRoot.innerHTML = ''
        this.$refs.appRoot.appendChild(appDom)
      }
      this.runChildLifeFn(to, from)
      this.systemLoading = false
    },
    // 执行子项目的生命周期
    async runChildLifeFn (to, from) {
      // 第一次进入系统
      if (!to && !from) {
        const name = window.QIANKUN_DATA.appConfig.activeRule
        await this.awaitUtilFn(() => this.appVM[name])
        this.setChildVm(this.appVM[name])
        this.callFn(this.appVM[name], 'onShow')
        return
      }
      // 系统切换时

      // 执行子系统退出事件
      this.callFn(this.appVM[from.path], 'onHide')
      this.callFn(this.appVM[from.path], 'deactivated')

      // 等待子项目加载完成
      await this.awaitUtilFn(() => this.appVM[to.path])
      this.setChildVm(this.appVM[to.path])
      this.callFn(this.appVM[to.path], 'onShow')
      this.callFn(this.appVM[to.path], 'activated')
    },

    async callFn (childVm, name) {
      const that = this
      if (!childVm || !childVm.$route) {
        console.error('子项目路由未加载完成')
        return
      }
      for (const matched of childVm.$route.matched) {
        await this.awaitUtilFn(() => {
          const options = matched.components?.default
          return options
        })
        const vm = matched.instances?.default // 首页没有vm实例
        if (!vm) {
          return
        }
        const options = matched.components?.default
        if (options && options[name]) {
          options[name].call(vm, {
            isFirst: !that.pageNav.find((it) => it.code === options.name)
          })
        }
      }
    },
    awaitUtilFn (fn, count = 30, time = 300) {
      if (fn()) {
        return
      }
      return new Promise((resolve, reject) => {
        const timer = setInterval(() => {
          count--
          if (count <= 0) {
            clearInterval(timer)
            reject(new Error('超时'))
            return
          }
          const ret = fn()
          if (fn()) {
            clearInterval(timer)
            resolve()
          }
        }, time)
      })
    },
    // 设置childVm
    setChildVm (childVm) {
      this.childVm = childVm
      window.QIANKUN_DATA.childVm = childVm
    },
    // 往子项目里面插入js、或改变某些特性；用来修复、新增功能
    insertJs (glob, ChildVue, childVm) {
      if (glob && ChildVue && childVm) {
        window.QIANKUN_DATA.ChildVue = ChildVue
        this.setChildVm(childVm)
        this.addAppVm()
        // this.flashRoute(childVm, ChildVue)
        this.appendTabsFn(childVm, ChildVue)
        this.appendSetMenuFn(ChildVue, childVm)
        this.appendComponents(ChildVue, childVm)
        this.appendUtils(ChildVue, childVm)
      } else {
        console.error(`请按要求添加生命周期：
            export async function update (obj) {
              obj.run(window, Vue, app)
            }`)
      }
    }
  }
}
