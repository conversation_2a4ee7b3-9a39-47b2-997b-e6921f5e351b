<template>
  <div class="performance-monitor" v-if="showMonitor">
    <div class="monitor-header">
      <h4>性能监控</h4>
      <div class="monitor-controls">
        <el-switch
          v-model="isMonitoring"
          @change="toggleMonitoring"
          active-text="监控中"
          inactive-text="已停止">
        </el-switch>
        <el-button size="mini" @click="showMonitor = false">
          <i class="el-icon-close"></i>
        </el-button>
      </div>
    </div>

    <div class="monitor-content">
      <!-- 内存使用情况 -->
      <div class="memory-section">
        <div class="section-title">内存使用</div>
        <div class="memory-info">
          <div class="memory-item">
            <span class="label">已使用:</span>
            <span class="value" :class="getMemoryClass(currentMemory.used)">
              {{ currentMemory.used }}MB
            </span>
          </div>
          <div class="memory-item">
            <span class="label">总计:</span>
            <span class="value">{{ currentMemory.total }}MB</span>
          </div>
          <div class="memory-item">
            <span class="label">使用率:</span>
            <span class="value" :class="getMemoryClass(currentMemory.used)">
              {{ currentMemory.usagePercent }}%
            </span>
          </div>
        </div>

        <!-- 内存使用进度条 -->
        <el-progress
          :percentage="currentMemory.usagePercent"
          :color="getProgressColor(currentMemory.used)"
          :show-text="false"
          :stroke-width="8">
        </el-progress>

        <!-- 内存趋势 -->
        <div class="memory-trend" v-if="memoryTrend">
          <span class="trend-label">5分钟趋势:</span>
          <span class="trend-value" :class="getTrendClass(memoryTrend.trend)">
            {{ memoryTrend.trend > 0 ? '+' : '' }}{{ memoryTrend.trend.toFixed(1) }}MB
            ({{ memoryTrend.rate.toFixed(2) }}MB/min)
          </span>
        </div>
      </div>

      <!-- 资源统计 -->
      <div class="resource-section">
        <div class="section-title">资源统计</div>
        <div class="resource-stats">
          <div class="stat-item">
            <span class="label">子应用:</span>
            <span class="value">{{ resourceStats.apps }}</span>
          </div>
          <div class="stat-item">
            <span class="label">媒体实例:</span>
            <span class="value">{{ resourceStats.instances }}</span>
          </div>
          <div class="stat-item">
            <span class="label">定时器:</span>
            <span class="value">{{ resourceStats.timers }}</span>
          </div>
          <div class="stat-item">
            <span class="label">事件监听:</span>
            <span class="value">{{ resourceStats.eventListeners }}</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="monitor-actions">
        <el-button size="mini" @click="performCleanup('light')">
          轻量清理
        </el-button>
        <el-button size="mini" type="warning" @click="performCleanup('deep')">
          深度清理
        </el-button>
        <el-button size="mini" type="danger" @click="forceGC">
          强制GC
        </el-button>
      </div>

      <!-- 内存历史图表 -->
      <div class="memory-chart" v-if="showChart">
        <div class="section-title">内存使用历史</div>
        <canvas ref="memoryChart" width="300" height="100"></canvas>
      </div>
    </div>
  </div>
</template>

<script>
import memoryMonitor from '@/utils/memoryMonitor'
import resourceManager from '@/utils/resourceManager'

export default {
  name: 'PerformanceMonitor',
  data () {
    return {
      showMonitor: false,
      isMonitoring: false,
      currentMemory: {
        used: 0,
        total: 0,
        usagePercent: 0
      },
      memoryTrend: null,
      resourceStats: {
        apps: 0,
        instances: 0,
        timers: 0,
        eventListeners: 0
      },
      showChart: false,
      updateTimer: null
    }
  },

  mounted () {
    // 监听键盘快捷键 Ctrl+Shift+M 打开监控面板
    document.addEventListener('keydown', this.handleKeydown)

    // 注册内存监控回调
    memoryMonitor.registerCleanupCallback(this.onMemoryCleanup, 'normal')

    // 开始更新数据
    this.startUpdating()
  },

  beforeDestroy () {
    document.removeEventListener('keydown', this.handleKeydown)
    memoryMonitor.unregisterCleanupCallback(this.onMemoryCleanup)
    this.stopUpdating()
  },

  methods: {
    handleKeydown (event) {
      // Ctrl+Shift+M 打开/关闭监控面板
      if (event.ctrlKey && event.shiftKey && event.key === 'M') {
        event.preventDefault()
        this.showMonitor = !this.showMonitor
      }
    },

    toggleMonitoring (enabled) {
      if (enabled) {
        memoryMonitor.startMonitoring()
      } else {
        memoryMonitor.stopMonitoring()
      }
    },

    startUpdating () {
      this.updateData()
      this.updateTimer = setInterval(this.updateData, 5000) // 每5秒更新一次
    },

    stopUpdating () {
      if (this.updateTimer) {
        clearInterval(this.updateTimer)
        this.updateTimer = null
      }
    },

    updateData () {
      // 更新内存信息
      const memoryInfo = memoryMonitor.getMemoryInfo()
      if (memoryInfo) {
        this.currentMemory = memoryInfo
      }

      // 更新内存趋势
      this.memoryTrend = memoryMonitor.getMemoryTrend()

      // 更新资源统计
      this.updateResourceStats()

      // 更新监控状态
      this.isMonitoring = memoryMonitor.isMonitoring

      // 更新图表
      if (this.showChart) {
        this.updateChart()
      }
    },

    updateResourceStats () {
      const resourceStats = resourceManager.getResourceStats()

      // 获取子应用数量
      const appCount = window.QIANKUN_DATA && window.QIANKUN_DATA.qiankunVm
        ? Object.keys(window.QIANKUN_DATA.qiankunVm.micApp || {}).length
        : 0

      this.resourceStats = {
        apps: appCount,
        instances: resourceStats.instances,
        timers: resourceStats.timers,
        eventListeners: resourceStats.eventListeners
      }
    },

    getMemoryClass (used) {
      if (used >= 1200) return 'critical'
      if (used >= 800) return 'warning'
      return 'normal'
    },

    getProgressColor (used) {
      if (used >= 1200) return '#f56c6c'
      if (used >= 800) return '#e6a23c'
      return '#67c23a'
    },

    getTrendClass (trend) {
      if (trend > 50) return 'trend-up-high'
      if (trend > 0) return 'trend-up'
      if (trend < -50) return 'trend-down-high'
      if (trend < 0) return 'trend-down'
      return 'trend-stable'
    },

    performCleanup (level) {
      if (level === 'light') {
        memoryMonitor.performLightCleanup()
      } else if (level === 'deep') {
        memoryMonitor.performDeepCleanup()
      }

      this.$message.success(`${level === 'light' ? '轻量' : '深度'}清理完成`)

      // 延迟更新数据以反映清理效果
      setTimeout(this.updateData, 1000)
    },

    forceGC () {
      memoryMonitor.forceGarbageCollection()
      this.$message.success('强制垃圾回收完成')
      setTimeout(this.updateData, 1000)
    },

    onMemoryCleanup (level) {
      this.$message.info(`执行了${level}级别的内存清理`)
    },

    updateChart () {
      const canvas = this.$refs.memoryChart
      if (!canvas) return

      const ctx = canvas.getContext('2d')
      const history = memoryMonitor.memoryHistory.slice(-20) // 最近20个数据点

      if (history.length < 2) return

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 绘制网格
      ctx.strokeStyle = '#e4e7ed'
      ctx.lineWidth = 1

      // 绘制内存使用曲线
      ctx.beginPath()
      ctx.strokeStyle = '#409eff'
      ctx.lineWidth = 2

      const maxMemory = Math.max(...history.map(h => h.used))
      const minMemory = Math.min(...history.map(h => h.used))
      const range = maxMemory - minMemory || 1

      history.forEach((point, index) => {
        const x = (index / (history.length - 1)) * canvas.width
        const y = canvas.height - ((point.used - minMemory) / range) * canvas.height

        if (index === 0) {
          ctx.moveTo(x, y)
        } else {
          ctx.lineTo(x, y)
        }
      })

      ctx.stroke()
    }
  },

  watch: {
    showMonitor (show) {
      if (show) {
        this.updateData()
        if (!this.isMonitoring) {
          this.toggleMonitoring(true)
        }
      }
    }
  }
}
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 9999;
  font-size: 12px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  background: #f5f7fa;
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  color: #303133;
}

.monitor-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.monitor-content {
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.section-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
  font-size: 13px;
}

.memory-section, .resource-section {
  margin-bottom: 15px;
}

.memory-info, .resource-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
}

.memory-item, .stat-item {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #606266;
}

.value {
  font-weight: bold;
}

.value.normal { color: #67c23a; }
.value.warning { color: #e6a23c; }
.value.critical { color: #f56c6c; }

.memory-trend {
  margin-top: 8px;
  font-size: 11px;
}

.trend-label {
  color: #909399;
}

.trend-up { color: #f56c6c; }
.trend-up-high { color: #f56c6c; font-weight: bold; }
.trend-down { color: #67c23a; }
.trend-down-high { color: #67c23a; font-weight: bold; }
.trend-stable { color: #909399; }

.monitor-actions {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.memory-chart {
  border-top: 1px solid #ebeef5;
  padding-top: 10px;
}

.memory-chart canvas {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 2px;
}
</style>
