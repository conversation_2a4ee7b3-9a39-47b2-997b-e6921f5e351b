export default {
  async flashCollectionList ({ commit }) {
    const list = await window.QIANKUN_DATA.qiankunVm.$api.getTabList({
      loginName: window.QIANKUN_DATA.user_info.user.loginName
    })
    commit('setCollectionList', list.sort((a, b) => a.sort - b.sort))
  },
  async toggleCollection (store, menuId) {
    const collectionItem = store.state.collectionList.find(it => it.menuId === menuId)
    const params = {
      loginName: window.QIANKUN_DATA.user_info.user.loginName,
      menuId
    }
    const $api = window.QIANKUN_DATA.qiankunVm.$api
    if (!collectionItem) {
      await $api.createTab(params)
      window.QIANKUN_DATA.childVm.$message({
        type: 'success',
        message: '收藏成功'
      })
    } else {
      await $api.delTab(params)
      window.QIANKUN_DATA.childVm.$message({
        type: 'success',
        message: '已取消收藏'
      })
    }
    await store.dispatch('flashCollectionList')
  }
}
