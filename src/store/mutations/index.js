import utils from '@/utils'
import task from './task'
import ext from './export'
import collection from './collection'
const mutations = {
  ...collection,
  ...ext,
  ...task,
  setUserInfo (state, module) {
    state.USER_INFO = module
  },
  // 微前端第一次进来获取tabs列表，让用户刷新还是存在tabList
  flashPageNav (state) {
    const loginName = window.QIANKUN_DATA.user_info.user.loginName
    // const pageNav = JSON.parse(sessionStorage[`qiankun_pageNav_${localPathname}_${userId}`] || '[]').filter(_ => _)
    const pageNav = JSON.parse(sessionStorage[`qiankun_pageNav_${loginName}`] || '[]').filter(_ => _)
    state.pageNav = pageNav.filter(it => it)
    saveLocalPageNav(state)
  },
  // 路由变化时添加tab页面，包括第一次进来
  addPageNavByRoute (state, route) {
    // 没有加载到USER_INFO
    const item = state.activePageNav
    if (!item) {
      return false
    }
    mutations.addPageNav(state, {
      qiankunKey: item.qiankunKey,
      pathname: route.path,
      fullPath: route.hash.replace('#', ''),
      path: route.hash.replace('#', '').split('?')[0],
      code: item.code,
      name: item.name,
      menuId: item.id,
      tenantId: item.tenantId,
      type: item.type,
      isShow: item.isShow
    })
    saveLocalPageNav(state)
  },
  // 添加tab页面，不打开
  addPageNav (state, item) {
    // 如：
    // fullPath: "/industry-dynamics/index?a=123"
    // path: "/industry-dynamics/index"
    // pathname: "/workspace"
    const { qiankunKey, pathname, fullPath, path, code, name, menuId, tenantId, type, isShow } = item
    const nav = state.pageNav.find(it => it.qiankunKey === qiankunKey)
    const navInfo = {
      pathname,
      fullPath,
      path,
      code,
      name,
      qiankunKey,
      menuId,
      tenantId,
      type,
      isShow
    }
    if (nav) { // 去重合并
      Object.assign(nav, navInfo)
    } else {
      // 同租户菜单放到一起
      const i = state.pageNav.findLastIndex((it) => { return it.tenantId === navInfo.tenantId })
      if (i === -1) {
        state.pageNav.push(navInfo)
      } else {
        state.pageNav.splice(i + 1, 0, navInfo)
      }
    }
  },
  // 批量添加收藏tab,批量打开收藏夹
  addPageNavList (state, list = []) {
    list.forEach(it => {
      mutations.addPageNav(state, { ...it, fullPath: it.href, path: it.href, type: 'router', isShow: '1' })
    })
    saveLocalPageNav(state)
  },
  setActivePageNav (state, route) {
    state.activePageNav = utils.getUserMenuInfo(route.hash.replace('#/', '').split('?')[0])
    window.QIANKUN_DATA.activePageNav = state.activePageNav
    saveLocalPageNav(state)
  },
  // 关闭当前标签页
  closeSelfPageNav (state, index) {
    state.pageNav.splice(index, 1)
    saveLocalPageNav(state)
  },
  // 关闭左侧标签页
  closeLeftPageNav (state, index) {
    state.pageNav = state.pageNav.slice(index)
    saveLocalPageNav(state)
  },
  // 关闭右侧标签页
  closeRightPageNav (state, index) {
    state.pageNav = state.pageNav.slice(0, index + 1)
    saveLocalPageNav(state)
  },
  // 关闭其他标签页
  closeOtherPageNav (state, index) {
    state.pageNav = [state.pageNav[index]]
    saveLocalPageNav(state)
  },
  // 只允许删除本系统的tab
  removeTabPageNav (state, path) {
    const index = state.pageNav.findIndex(it => (it.path === path || it.fullPath === path) && it.pathname === location.pathname)
    if (index !== -1) {
      state.pageNav.splice(index, 1)
    }
    saveLocalPageNav(state)
  },
  // 拖拽排序
  SORT_PAGE_NAV (state, { oldIndex, newIndex }) {
    const oldItem = state.pageNav.splice(oldIndex, 1)
    state.pageNav.splice(newIndex, 0, oldItem[0])
    saveLocalPageNav(state)
  }
}

export default mutations

// 长存储pageNav
const localPathname = location.pathname.replace(/\//g, '')
function saveLocalPageNav (state) {
  const loginName = window.QIANKUN_DATA.user_info.user.loginName
  sessionStorage[`qiankun_pageNav_${loginName}`] = JSON.stringify(state.pageNav)
  // sessionStorage[`qiankun_pageNav_${localPathname}_${userId}`] = JSON.stringify(state.pageNav)
}

function getPathnameByHostName (tenantId) {
  const href = window.QIANKUN_DATA.hostMap[tenantId]
  if (!href) {
    return
  }
  const match = href.match(/\/([^\\/]+)\/?$/)
  return match ? `/${match[1]}` : null
}
