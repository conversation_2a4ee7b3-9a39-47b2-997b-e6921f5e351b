<template>
  <div class="qiankun-cp-debunk">
    <el-button class="debunk-btn" icon="hermes hermes-ty-yjfk" @click="dialogVisible = true"
      >意见反馈</el-button
    >
    <el-dialog
      title="意见反馈"
      :visible.sync="dialogVisible"
      width="500px"
      :modal="false"
      append-to-body
      custom-class="qiankun-cp-debunk-dialog"
    >
      <span>
        <div class="qk-icons">
          <div class="qk-icon bad" :class="{ selectd: selectType === 'bad' }">
            <i class="hermes" :class="selectType === 'bad'?'hermes-yjfk-tc-y':'hermes-yjfk-tc-n'" @click="selectType = 'bad'"></i>
            <span>吐个槽</span>
          </div>
          <div class="qk-icon good" :class="{ selectd: selectType === 'good' }">
            <i class="hermes" :class="selectType === 'good'?'hermes-yjfk-dz-y':'hermes-yjfk-dz-n'" @click="selectType = 'good'"></i>
            <span>点个赞</span>
          </div>
        </div>
        <el-input
          type="textarea"
          v-model="content"
          :disabled="selectType !== 'bad'"
          :autosize="{ minRows: 5, maxRows: 5 }"
          placeholder="请输入你的吐槽内容..."
        />
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data () {
    return {
      selectType: '',
      dialogVisible: false,
      content: ''
    }
  },
  methods: {
    async getState () {
      const data = await this.$api.greaState()
      return data
    },
    async submit () {
      if (!this.selectType) {
        this.$message.warning('请选择反馈项哦~')
        return false
      }
      if (this.selectType === 'good') {
        const hasGood = await this.getState()
        if (hasGood) {
          this.$message.warning('您今天已经点过赞了哦~')
          return false
        } else {
          await this.$api.feedbackCreate({
            great: 1
          })
        }
      } else {
        await this.$api.feedbackCreate({
          great: 0,
          content: this.content
        })
      }
      this.$message.success('操作成功')
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="less">
.qiankun-cp-debunk {
  .debunk-btn {
    position: fixed;
    width: 160px;
    margin-left: 10px;
    bottom: 10px;
    background: #3e4965;
    height: 40px;
    font-size: 14px;
    color: #B6BAC5;
    border: none;
    &:hover{
      background-color: #3e4965;
      color: #fff;
    }
    i{
      margin-right: 6px;
      margin-top: 2px;
      font-size: 14px;
      position: relative;
      top: 1px;
    }
  }
}
</style>
<style lang="less">
.qiankun-cp-debunk-dialog {
  .qk-icons {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 10px;
    .qk-icon {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &.good.selectd {
        i {
          color: rgb(255, 171, 0);
        }
      }
      &.bad.selectd {
        i {
          color: rgb(222, 53, 9);
        }
      }
      i {
        cursor: pointer;
        font-size: 30px;
        background: #eef2f6;
        text-align: center;
        border-radius: 1000px;
        width: 60px;
        height: 60px;
        line-height: 60px;
      }
      span {
        font-size: 12px;
        margin-top: 14px;
        margin-bottom: 50px;
      }
    }
  }
  .el-textarea {
    max-width: 1000px;
  }
}
</style>
