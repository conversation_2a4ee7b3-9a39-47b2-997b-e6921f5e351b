<template>
    <div class="sy-iframe-alive" v-show="!isIframePage">
          <slot />
    </div>
  </template>

<script>
import syIframeAliveChild from './sy-iframe-alive-child.vue'
export default {
  props: {
    activePages: {}, // 将keep-alive上记录页面打开标签的code数组传递过来
    iframeCodes: {} // 输入需要缓存的iframe页面的code
  },
  mounted () {
    setTimeout(() => {
      this.mountComponent()
    }, 0)
  },
  data () {
    return {
      active: window.QIANKUN_DATA.active,
      comps: {},
      hasActivePage: new Set()
    }
  },
  watch: {
    iframeCodes: {
      handler () {
        this.setComponents()
      },
      deep: true,
      immediate: true
    },
    activePages: {
      handler () {
        this.setCompProp()
      }
    },
    isIframePage: {
      handler () {
        this.setCompProp()
      },
      deep: true,
      immediate: true
    },
    $route: {
      handler () {
        this.setCompProp()
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 当前页面是否为iframe
    isIframePage () {
      return this.getIsIframePage()
    },
    hasOpenComponentsArr () {
      return this.activePages.map(code => this.comps[code]).filter(_ => _)
    }
  },
  methods: {
    // 设置组件属性
    setCompProp () {
      if (!this.instance) {
        return
      }
      this.hasActivePage.add(this.$route.name) // 记录当前页面打开的页面
      this.instance.hasOpenComponentsArr = this.hasOpenComponentsArr.filter(it => this.hasActivePage.has(it.name)) // 只有打开过得页面才会渲染
      this.instance.isIframePage = this.getIsIframePage()
      this.instance.myRoute = this.$route || {}
    },
    getIsIframePage () {
      console.log('getIsIframePage', this.comps, this.$route.name, this.active, window.QIANKUN_DATA.active)
      if (this.active !== window.QIANKUN_DATA.active) {
        return false
      }
      return !!this.comps[this.$route.name]
    },
    // 设置iframe组件
    setComponents () {
      const router = this.$router
      const routes = router.options.routes || []
      const childRouters = routes.find(item => item.path === '/')
      let allComps = [...routes]
      if (childRouters && childRouters.children) {
        allComps = [...routes, ...childRouters.children]
      }
      allComps.forEach(item => {
        if (this.iframeCodes.includes(item.name)) {
          this.comps[item.name] = item
        }
      })
    },
    mountComponent () {
      // 创建组件实例
      const ComponentClass = window.QIANKUN_DATA.ChildVue.extend(syIframeAliveChild)
      this.instance = new ComponentClass({ propsData: { $route: this.$route } })

      // 挂载组件，但不设置挂载目标，Vue将创建一个div元素
      this.instance.$mount()

      // 将组件的DOM元素插入到body或者其他元素中
      document.getElementById('qiankun-dom-cache').appendChild(this.instance.$el)

      this.setCompProp()
    }
  },
  beforeDestroy () {
    if (this.instance) {
      document.getElementById('qiankun-dom-cache').removeChild(this.instance.$el)
      // 销毁子组件实例
      this.instance.$destroy()
      this.instance = null
    }
  }
}
</script>
<style>
.sy-iframe-alive{
  width: 100%;
  height: 100%
}
</style>
