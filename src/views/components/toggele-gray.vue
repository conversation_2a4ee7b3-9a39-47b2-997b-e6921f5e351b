<template>
  <div class="qiuankun-toggele-gray" :class="{ is_gray_env:isGray }" @click="toggleGray">
    <i class="icon-toggle hermes hermes-huanjingqiehuan"></i>
    <span class="text">{{ isGray ? "切换到测试" : "切换到灰度" }}</span>
  </div>
</template>
<script>
export default {
  name: 'toggele-gray',
  data () {
    return {
      isGray: window.QIANKUN_SETTING.isGray
    }
  },
  methods: {
    toggleGray () {
      if (this.isGray) {
        location.pathname = location.pathname.replace('-gray', '')
      } else {
        location.pathname = location.pathname.replace(/\//g, '') + '-gray'
      }
    }
  }
}
</script>
<style lang="less">
@red: #dd360a;
@gray: #6f778c;
.qiuankun-toggele-gray {
  z-index: 1000;
  position: fixed;
  right: -100px;
  bottom: 100px;
  background: @gray;
  overflow: hidden;
  border-radius: 10000px;
  display: flex;
  padding: 10px 30px 10px 10px;
  cursor: pointer;
  transition: all 0.2s;
  &:hover {
    right: -20px;
  }
  &.is_gray_env {
    background: @red;
    .icon-toggle {
      color: @red;
    }
  }
  .icon-toggle {
    display: inline-block;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    width: 30px;
    height: 30px;
    border-radius: 1000px;
    background: #ffffff;
    color: @gray;
    font-size: 14px;
  }
  .text {
    color: #ffffff;
    font-size: 14px;
    margin-left: 10px;
    line-height: 30px;
  }
}
</style>
