<template>
  <div v-loading="loading" element-loading-text="拼命加载中" style="height:100%">
    <div class="content-view">
      <div class="content-view-top">
        <div class="content-view-top-title">
          {{info.title}}
        </div>
        <div class="content-view-top-list">
          <p>发布时间： {{info.createDate}}</p>
          <p>创建人：{{info.createBy}}</p>
          <p><i class="el-icon-view"></i>{{info.viewCount}}浏览</p>
        </div>
        <el-divider></el-divider>
        <!-- <div class="content-view-top-image">
          <el-image style="width: 100%; height: 100%;border-radius: 38px" :src="info.image" fit='contain'></el-image>
        </div> -->
      </div>
      <div class="content-view-center" v-if="info.izJumpLink==='1'" style="height:100%">
        <iframe :src="info.jumpLink" width="100%" height="100%">
        </iframe>
      </div>
      <div class="content-view-center" v-else>
        <sy-editor v-model="info.content" :editorOption='editorOption' ref="syEditor"></sy-editor>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'qiankun-staging-content-view',
  data () {
    return {
      info: {},
      loading: true,
      editorOption: { modules: { toolbar: {} } }
    }
  },
  props: ['self'],
  computed: {},
  async mounted () {
    this.info = await this.$api.hermesWorkspaceNewsGet({ id: this.self.$route.query.id })
    this.loading = false
    if (this.$refs.syEditor) {
      this.$refs.syEditor.$children[0]._props.disabled = true
    }
  }

}
</script>

<style lang="less" scoped>
.content-view {
  width: 753px;
  margin: 0 auto;
  height: 100%;
}
.content-view-top-title {
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  color: #0d1b3f;
  margin-top: 40px;
}
.content-view-top-list {
  display: flex;
  margin-top: 10px;
  p {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(110, 118, 140, 1);
    margin-right: 20px;
    i {
      margin-right: 6px;
    }
  }
}
.content-view-top-image {
  width: 100%;
  height: 405px;
  border-radius: 38px;
}
.content-view-center {
  margin-top: 20px;
  width: 100%;
  padding-bottom: 20px;
  font-size: 14px;
}
</style>
<style >
.content-view-center img {
  max-width: 100%;
  margin: 0 auto;
  display: block;
}
.list-container-auto-view {
  height: auto !important;
  min-height: 100%;
}
.content-view-center .ql-container.ql-snow {
  border: none;
}
.content-view-center .ql-toolbar.ql-snow {
  display: none;
}
</style>
