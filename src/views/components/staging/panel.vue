<template>
  <div class="staging">
    <!-- 上半区 -->
    <div class="staging-top">
      <!-- 我的待办 -->
      <div class="my-todo">
        <div class="staging-title">我的待办</div>
        <div v-loading="todosLoading">
          <el-tabs v-model="activeName" v-if="todos.length">
            <el-tab-pane label="KOL" name="first">
              <div class="el-tabs-warp">
                <div class="my-todo-piece" v-for="(item,index) in todos" :key="index" @click="todoSkipPage(item)">
                  <div class="my-todo-piece-tag one-line-ellipsis" :class="item.category">{{ item.categoryName }}</div>
                  <div class="my-todo-piece-number">{{ item.num }}</div>
                  <div class="my-todo-piece-text">{{ item.name }}</div>
                  <!-- <img src="https://testoss.syounggroup.com/static/file/defaultTenantId/工作台-待处理图片.png" /> -->
                </div>
              </div>
              <div class="staging-pagination">
                <el-pagination @current-change="listTodoByUser"
                  :current-page.sync="currentPage"
                  :hide-on-single-page="true"
                  :page-size="8"
                  layout="prev, pager, next, jumper"
                  :total="todoTotal">
                </el-pagination>
              </div>
            </el-tab-pane>
          </el-tabs>
          <!-- 无数据 -->
          <div class="staging-empty-block staging-empty-block-img" v-else>
            <img src="./../../../assets/staging/todo-null.png" />
            <div>当前暂无待办</div>
          </div>
        </div>
      </div>
      <!-- 上新资讯 -->
      <div class="new-information">
        <div class="staging-title">上新资讯</div>
        <div v-loading="newsLoading">
          <!-- 有数据 -->
          <div class="new-warp-has-data" v-if="newInformation.length">
            <div class="new-warp">
              <div class="staging-new-information-block" v-for="(item,index) in newInformation" :key="index" @click="isJumpLink(item)">
                <img :src="item.image" />
                <div class="staging-new-information-block-right">
                  <div class="one-line-ellipsis">{{ item.title }}</div>
                  <div class="one-line-ellipsis">{{ item.description }}</div>
                  <div>{{ item.createDate }}</div>
                </div>
              </div>
            </div>
            <div class="staging-pagination">
              <el-pagination @current-change="listNewsByUser"
                :current-page.sync="newsCurrentPage"
                :hide-on-single-page="true"
                :page-size="4"
                layout="prev, pager, next, jumper"
                :total="newsTotal">
              </el-pagination>
            </div>
          </div>
          <!-- 无数据 -->
          <div class="staging-empty-block staging-empty-block-img" v-else>
            <img src="./../../../assets/staging/news-null.png">
            <div>当前暂无上新资讯</div>
          </div>
        </div>
      </div>
    </div>
    <!--下半区-->
    <div class="staging-bottom">
      <!-- 最近访问 -->
      <div class="recent-visit">
        <div class="staging-title">最近访问</div>
        <div v-loading="visitsLoading">
          <!-- 有数据 -->
          <div v-if="visits.length" class="staging-tag">
            <div class="staging-tag-item" v-for="(item,index) in visits" :key="index" @click="skipPage(item.modulePath)">
              <sy-svg v-if="/^svg-/.test(item.icon)" :icon-class="item.icon.slice(4)" />
              <i v-else-if="typeof item.icon === 'string' && item.icon.startsWith('el-')" class="menu-icon" :class="item.icon"></i>
              <i v-else class="menu-icon hermes" :class="item.icon || 'hermes-xitongguanli1'"></i>
              {{item.moduleName}}
            </div>
          </div>
          <!-- 无数据 -->
          <div class="staging-empty-block" v-else>
            <div>暂无数据，快去使用功能吧~</div>
          </div>
        </div>
      </div>
      <!-- 我的收藏夹 -->
      <div class="my-collect">
        <div class="staging-title">我的收藏夹</div>
        <div v-loading="collectLoading">
          <!-- 有数据 -->
          <div v-if="collect.length" class="staging-tag">
            <div class="staging-tag-item" v-for="(item,index) in collect" :key="index" @click="skipPage(item.modulePath)">
              <sy-svg v-if="/^svg-/.test(item.icon)" :icon-class="item.icon.slice(4)" />
              <i v-else-if="typeof item.icon === 'string' && item.icon.startsWith('el-')" class="menu-icon" :class="item.icon"></i>
              <i v-else class="menu-icon hermes" :class="item.icon || 'hermes-xitongguanli1'"></i>
              {{item.moduleName}}
            </div>
          </div>
          <!-- 无数据 -->
          <div class="staging-empty-block" v-else>
            <div>暂无收藏</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'qiankun-staging-panel',
  components: {},
  data () {
    return {
      activeName: 'first',
      currentPage: 1,
      newsCurrentPage: 1,
      todoTotal: 0,
      newsTotal: 0,
      todos: [],
      newInformation: [],
      visits: [],
      collect: [],
      collectLoading: false,
      visitsLoading: false,
      todosLoading: false,
      newsLoading: false
    }
  },
  props: ['self'],
  watch: {},
  computed: {},
  methods: {
    init () {
      // 获取我的收藏夹
      this.listFavoriteModuleByUser()
      // 获取我的资讯
      this.listNewsByUser()
      // 获取我的待办
      this.listTodoByUser()
      // 获取我的常用功能
      this.listTopUsedModuleByUser()
    },
    // 获取我的收藏夹
    async listFavoriteModuleByUser () {
      try {
        this.collectLoading = true
        const data = await this.$api.listFavoriteModuleByUser()
        this.collect = Object.assign(data)
      } catch (error) {
        console.log(error)
      } finally {
        this.collectLoading = false
      }
    },
    // 获取我的资讯
    async listNewsByUser () {
      try {
        this.newsLoading = true
        const data = await this.$api.listNewsByUser({
          pageNo: this.newsCurrentPage,
          pageSize: 4
        })
        this.newsTotal = data.total
        this.newInformation = Object.assign(data?.list, [])
      } catch (error) {
        console.log(error)
      } finally {
        this.newsLoading = false
      }
    },
    // 获取我的待办
    async listTodoByUser () {
      try {
        this.todosLoading = true
        const data = await this.$api.listTodoByUser({
          pageNo: this.currentPage,
          pageSize: 8
        })
        this.todoTotal = data.total
        this.todos = Object.assign(data?.list, [])
      } catch (error) {
        console.log(error)
      } finally {
        this.todosLoading = false
      }
    },
    // 获取我的常用功能
    async listTopUsedModuleByUser () {
      try {
        this.visitsLoading = true
        const data = await this.$api.listTopUsedModuleByUser()
        this.visits = Object.assign(data)
      } catch (error) {
        console.log(error)
      } finally {
        this.visitsLoading = false
      }
    },
    // 待办跳转
    todoSkipPage (item) {
      // 如果是全案达人，则为弹窗
      const iframeTypeList = ['COMPLETE_SCHEME_TALENT_WAIT_CHECK',
        'COMPLETE_SCHEME_TALENT_WAIT_CONFIRM',
        // 'COMPLETE_SCHEME_TALENT_WAIT_APPROVE',
        'COMPLETE_SCHEME_TALENT_WAIT_CHOOSE',
        'FAILED_OBTAIN_EFFECT_DATA',
        'TALENT_HOME_PAGE_LINK_ERROR']

      if (iframeTypeList.includes(item.type)) {
        this.$emit('open', item)
        // this.slotParam = item
      } else if (item.url) {
        this.skipPage(item.url)
      } else {
        this.$message.warning('未获取当前跳转路径')
      }
    },
    // 页面跳转
    skipPage (path) {
      if (path) {
        this.self.$router.push(path)
      } else {
        console.log('跳转失败,无路径返回')
      }
    },
    isJumpLink (item) {
      if (item.izJumpLink === '1') {
        window.open(item.jumpLink)
      } else {
        this.self.$router.push(`/staging/view?id=${item.id}`)
      }
    }
  },
  created () {
    this.init()
  },
  mounted () { }
}
</script>
<style lang="less">
.staging {
  * {
    box-sizing: border-box;
    // line-height: 1em;
  }
  display: flex;
  flex-direction: column;
  min-height: 100%;
  .staging-title {
    &:first-child{
      margin-bottom: 10px;
    }
    font-weight: bold;
    font-size: 16px;
    line-height: 22px;
    color: #0d1b3f;
  }
  .staging-pagination {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 23px;
    width: 100%;
  }
  .staging-empty-block {
    min-height: 84px;
    &.staging-empty-block-img {
      min-height: 400px;
    }
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #3e4965;
    font-weight: bold;
    img {
      margin-bottom: 10px;
      width: 180px;
    }
  }
  .one-line-ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .staging-tag {
    display: flex;
    flex-wrap: wrap;
    padding-top: 6px;
    overflow: hidden;
    max-height: 105px;
    .staging-tag-item {
      height: 34px;
      font-size: 14px;
      padding: 8px 18px;
      color: #6e768c;
      background: #f0f2f6;
      border-radius: 4px;
      margin: 0 16px 16px 0;
      cursor: pointer;
      &:hover {
        background: #efebfa;
        color: #5f3bce;
      }
    }
  }
  .staging-top {
    display: flex;
    margin-bottom: 8px;
    .my-todo,
    .new-information {
      background-color: #fff;
      width: 50%;
      .el-tabs{
        .el-tabs__nav.is-top{
          padding:0 10px;
        }
        .el-tabs__active-bar.is-top{
          width: 50px !important;
        }
      }
      .el-tabs .el-tabs__item.is-active{
        color: #5F3BCE;
      }
      .el-tab-pane {
        height: 404px;
        padding-top: 12px;
        position:relative;
      }
      .el-tabs-warp {
        display: flex;
        // height: 344px;
        justify-self: flex-start;
        flex-wrap: wrap;

        & > div {
          position: relative;
          cursor: pointer;
          margin: 0 12px 12px 0;
          border-radius: 4px;
          width: calc((100% - 36px) / 4);
          background-color: #f0f2f6;
          background-image: url(https://testoss.syounggroup.com/static/file/defaultTenantId/工作台-待处理图片.png);
          background-size: 63px 55px;
          background-position: bottom right;
          background-repeat: no-repeat;
          &:nth-of-type(4n + 0) {
            margin-right: 0;
          }
          &:hover {
            background-color: #efebfa;
          }
          .my-todo-piece-tag {
            border-radius: 4px 0px 4px 0px;
            padding: 2px 8px 2px 8px;
            font-size: 12px;
            height: 24px;
            line-height: 20px;
            display: inline-block;
            max-width: 100%;
            color: #fff;
            background-color: #5f3bce;
            &.LAUNCH_TODO {
              background-color: #5f3bce;
            }
            &.ORG_TALENT_TODO {
              background-color: #ffab00;
            }
            &.PROVIDER_ORDER_TODO {
              background-color: #00b8d9;
            }
            &.LAUNCH_LIVE_TODO {
              background-color: #de3509;
            }
            &.TALENT_LIVE_BILL_TODO {
              background-color: #19a85a;
            }
            &.ORG_PROJECT_TODO {
              background-color: #19a897;
            }
          }
          .my-todo-piece-number {
            font-weight: 600;
            font-size: 20px;
            line-height: 28px;
            color: #5f3bce;
          }
          .my-todo-piece-text {
            font-weight: 400;
            font-size: 14px;
            line-height: 22px;
            color: #6e768c;
            margin-left: 14px;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          img {
            width: 63px;
            height: 55px;
            position: absolute;
            bottom: 0;
            right: 0;
          }
        }
      }
      .new-warp-has-data{
        height: 447px;
        position: relative;
      }
      .staging-new-information-block {
        height: 84px;
        width: 100%;
        display: flex;
        margin-top: 15px;
        cursor: pointer;
        img {
          margin-right: 16px;
          width: 126px;
          height: 84px;
        }
        &:hover {
          .staging-new-information-block-right {
            & > div:first-child {
              color: #5f3bce;
            }
          }
        }
        .staging-new-information-block-right {
          display: flex;
          flex-direction: column;
          color: #6e768c;
          width: calc(100% - 142px);
          font-size: 12px;
          position: relative;
          & > div:first-child {
            font-size: 14px;
            height: 22px;
            font-weight: bold;
            color: #0d1b3f;
          }
          & > div:nth-child(3) {
            position: absolute;
            bottom: 0;
          }
        }
      }
    }
    .my-todo {
      margin-right: 8px;
      .el-tabs__nav-scroll {
        border-bottom: 1px solid #e6e8eb;
      }
    }
  }
  .staging-bottom {
    background-color: #fff;
    // border-bottom: 16px solid #ebebeb;
    .recent-visit {
      padding-bottom: 8px;
      border-bottom: 1px solid #e6e8eb;
    }
  }
}
// @media screen and (min-width: 1401px) {
  .staging {
    // padding: 8px;
    .staging-top {
      height: 500px;
      .el-tabs-warp {
        margin-top: 5px;
      }
      .my-todo,
      .new-information {
        padding: 16px;
        .my-todo-piece {
          height: 135px;
          .my-todo-piece-number {
            margin: 20px 0 5px 14px;
          }
        }
      }
    }
    .staging-bottom {
      padding: 16px;
      // border-bottom: 16px solid #ebebeb;
      flex: 1;
      .my-collect {
        margin-top: 24px;
      }
    }
  }
// }
// @media screen and (min-width: 200px) and (max-width: 1400px) {
//   .staging {
//     .staging-tag {
//       .staging-tag-item {
//         margin: 8px 8px 0 0;
//       }
//     }
//     .staging-top {
//       height: 350px;
//       margin-bottom: 8px;
//       .my-todo {
//         margin-right: 8px;
//       }
//       .my-todo,
//       .new-information {
//         padding: 8px;
//         .my-todo-piece {
//           height: 80px;
//           .my-todo-piece-number {
//             margin: 3px 0 0 14px;
//           }
//         }
//         .staging-new-information-block {
//           height: 60px;
//           margin-top: 8px;
//           img {
//             margin-right: 16px;
//             width: 75px;
//             height: 60px;
//           }
//           .staging-new-information-block-right {
//             & > div:nth-child(2) {
//               margin: 5px 0 10px 0;
//             }
//           }
//         }
//       }
//     }
//     .staging-bottom {
//       padding: 8px;
//       height: calc(100% - 350px);
//       .my-collect {
//         height: calc(50% - 8px);
//         margin-top: 8px;
//       }
//     }
//   }
// }
</style>
