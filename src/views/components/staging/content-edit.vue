<template>
  <div v-loading="loading" element-loading-text="拼命加载中" >
    <div class="content-edit">
      <sy-normal-form v-bind="form" ref="form" />
    </div>
    <div class="content-edit-boottm">
      <sy-button v-for="(item,idx) in btns" :key="idx" v-bind="item" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'qiankun-staging-content-edit',
  data () {
    return {
      infoFlag: 1,
      loading: true
    }
  },
  props: ['self'],
  computed: {
    form () {
      const that = this
      return {
        btns: [],
        data: [
          {
            type: 'form',
            name: 'contentForm',
            data: {
              list (data) {
                return [
                  {
                    tag: 'sy-select',
                    prop: 'type',
                    label: '内容类型',
                    bind: {
                      options: [{ label: '上新资讯', value: 'NEW_ARRIVAL' }]
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'el-input',
                    prop: 'title',
                    label: '标题',
                    fullRow: true,
                    bind: {
                      showWordLimit: true
                    },
                    attrs: {
                      maxlength: 50
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'sy-upload',
                    prop: 'image',
                    label: '封面图',
                    bind: {
                      type: 'image',
                      accept: ['png', 'jpg', 'jpeg'],
                      bind: {
                        limit: 1
                      }
                    },
                    tip: {
                      type: 'text',
                      content: '建议尺寸795*530，小于1M的JPG PNG JEPG'
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'el-input',
                    prop: 'description',
                    label: '简介',
                    fullRow: true,
                    bind: {
                      type: 'textarea',
                      autosize: { minRows: 4 },
                      showWordLimit: true
                    },
                    attrs: {
                      maxlength: 50
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'sy-radio',
                    prop: 'izJumpLink',
                    label: '是否跳转外链',
                    bind: {
                      options: [
                        {
                          label: '是',
                          value: '1'
                        },
                        {
                          label: '否',
                          value: '0'
                        }
                      ]
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'sy-editor',
                    hide: data.izJumpLink === '1',
                    prop: 'content',
                    label: '正文',
                    fullRow: true,
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'el-input',
                    hide: data.izJumpLink !== '1',
                    prop: 'jumpLink',
                    label: '外链地址',
                    bind: {
                      type: 'textarea',
                      autosize: { minRows: 2 },
                      showWordLimit: true
                    },
                    attrs: {
                      maxlength: 500
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  },
                  {
                    tag: 'sy-select',
                    prop: 'authType',
                    label: '接收对象',
                    bind: {
                      options: [{ label: '所有用户', value: 'ALL' }]
                    },
                    itemBind: {
                      rules: [that.$utils.RULES.RULES_REQUIRE]
                    }
                  }
                ]
              }
            }
          }
        ],
        async save ({
          values
        }) {
          // console.log('values2', values)
          that.loading = true
          if (that.infoFlag === 1) {
            const res = await this.$api.hermesWorkspaceNewsCreate({ ...values.contentForm, image: values.contentForm.image[0].url })
            that.self.$closeTabs('/staging/create')
          } else if (that.infoFlag === 2) {
            const res = await this.$api.hermesWorkspaceNewsUpdate({ ...values.contentForm, image: values.contentForm.image[0].url })
            that.self.$closeTabs('/staging/editstaging')
          } else {
            const res = await this.$api.hermesWorkspaceNewsSubmitDraft({ ...values.contentForm, image: values.contentForm.image[0].url })
          }
          that.loading = false
          that.closeTabl()
          that.self.$router.push({ path: '/staging/list' })
        }
      }
    },
    btns () {
      const that = this
      return [
        {
          text: '提交',
          type: 'primary',
          debounce: true,
          call ({ handlerSave, backClose, disabled }) {
            that.$refs.form.handlerSave()
          }
        },
        {
          text: '保存草稿',
          debounce: true,
          async call ({ handlerSave, backClose, disabled }) {
            that.infoFlag = 3
            that.$refs.form.$refs.contentForm.validate()
            that.$refs.form.$clearValidate('contentForm', ['type', 'image', 'description', 'izJumpLink', 'content', 'jumpLink', 'authType'])
            const data = that.$refs.form.getValue('contentForm')
            if (!data.title) { return false }
            const res = await that.$api.hermesWorkspaceNewsSubmitDraft({ ...data, image: data.image[0] ? data.image[0].url : '' })
            that.closeTabl()
            that.self.$router.push({ path: '/staging/list' })
          }
        },
        {
          text: '返回',
          confirm: '你确定返回吗？',
          debounce: true,
          call ({ handlerSave, backClose, disabled }) {
            that.closeTabl()
            that.self.$router.push({ path: '/staging/list' })
          }
        }
      ]
    }
  },
  async mounted () {
    if (this.self.$route.query.id) {
      this.infoFlag = 2
      const res = await this.$api.hermesWorkspaceNewsGet({ id: this.self.$route.query.id })
      this.$refs.form.setValue('contentForm', { ...res, image: [{ url: res.image }] })
      this.loading = false
    } else {
      this.infoFlag = 1
      this.$refs.form.setValue('contentForm', { izJumpLink: '0', title: '' })
      this.loading = false
    }
  },
  methods: {
    closeTabl () {
      if (this.infoFlag === 1) {
        this.self.$closeTabs('/staging/create')
      } else {
        this.self.$closeTabs('/staging/edit')
      }
    }
  }

}
</script>

<style lang="less">
.form-item-htmlText5.form-item-htmlText.form-item-sy-editor {
  //   .el-form-item__content {
  //     width: calc(100% - 150px) !important;
  //     max-width: 100% !important;
  //   }
  .ql-editor.ql-blank {
    min-height: 100px;
  }
}
.content-edit-boottm {
  display: flex;
  padding: 17px 0px;
  width: calc(100% - 16px);
  justify-content: center;
  align-items: center;
  background: #fff;
  box-shadow: 0px -4px 4px 0px #eff2f5;
  position: fixed;
  bottom: 0px;
  left: 8px;
}
</style>
