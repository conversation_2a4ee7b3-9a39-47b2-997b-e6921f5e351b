<template>
  <div>
    <sy-normal-table v-bind="table" ref="table" />
  </div>
</template>
<script>
export default {
  name: 'qiankun-staging-content-list',
  data () {
    return {
      typeMap: [{ label: '上新资讯', value: 'NEW_ARRIVAL' }]
    }
  },
  methods: {
  },
  props: ['self'],
  computed: {
    table () {
      const that = this
      return {
        initSearch: false,
        filters (filtersValue, extraData) {
          return [
            {
              tag: 'sy-select',
              prop: 'status',
              label: '状态',
              bind: {
                options: [{ label: '已发布', value: 'PUBLISH' }, { label: '已下架', value: 'UN_PUBLISH' }, { label: '草稿', value: 'DRAFT' }]
              }
            },
            {
              tag: 'sy-select',
              prop: 'type',
              label: '内容类型',
              bind: {
                options: that.typeMap
              }
            },
            {
              tag: 'el-date-picker',
              prop: ['startCreateDate', 'endCreateDate'],
              label: '创建时间',
              bind: {
                type: 'daterange',
                valueFormat: 'yyyy-MM-dd',
                rangeSeparator: '-',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期'
              }
            },
            {
              tag: 'el-date-picker',
              prop: ['startUpdateDate', 'endUpdateDate'],
              label: '更新时间',
              bind: {
                type: 'daterange',
                valueFormat: 'yyyy-MM-dd',
                rangeSeparator: '-',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期'
              }
            },
            {
              tag: 'el-input',
              prop: 'queryText',
              label: '标题&简介',
              bind: {
                placeholder: '请输入标题、简介'
              }
            }
          ]
        },
        btns: [
          {
            text: '新建',
            type: 'primary',
            call () {
              that.self.$router.push({ path: '/staging/create' })
            }
          },
          {
            text: '批量上架',
            confirm: '你确定批量上架吗？',
            async call ({ tableData, filtersValue, selection, pageFilter, utils }, vm) {
              if (!selection.length) return this.$message.error('请先选择上架数据！')
              if (selection.filter(el => el.status === 'DRAFT').length > 0) {
                return this.$message.error('草稿状态的内容请使用“编辑”功能进行发布上架！')
              }
              const ids = selection.map(item => item.id)
              await that.$api.hermesWorkspaceNewsPublishByIds(ids)
              that.$refs.table.handlerSearch()
              that.$message.success('操作成功')
            }
          },
          {
            text: '批量下架',
            confirm: '你确定批量下架吗？',
            async call ({ tableData, filtersValue, selection, pageFilter, utils }, vm) {
              if (!selection.length) return this.$message.error('请先选择下架数据！')
              if (selection.filter(el => (el.status === 'UN_PUBLISH' || el.status === 'DRAFT')).length > 0) {
                return this.$message.error('不支持非已发布状态数据批量下架操作！')
              }
              const ids = selection.map(item => item.id)
              await that.$api.hermesWorkspaceNewsCancelPublishByIds(ids)
              that.$refs.table.handlerSearch()
              that.$message.success('操作成功')
            }
          },
          {
            text: '批量删除',
            confirm: '你确定批量删除吗？',
            async call ({ tableData, filtersValue, selection, pageFilter, utils }, vm) {
              if (!selection.length) return this.$message.error('请先选择删除数据！')
              if (selection.filter(el => el.status === 'PUBLISH').length > 0) {
                return this.$message.error('已发布状态不允许删除！')
              }
              const ids = selection.map(item => item.id)
              await that.$api.hermesWorkspaceNewsDeleteByIds(ids)
              that.$refs.table.handlerSearch()
              that.$message.success('操作成功')
            }
          }
        ],
        columns (data, self) {
          return [
            {
              type: 'selection'
            },
            {
              type: 'image',
              prop: 'image',
              label: '封面',
              itemBind: {
                width: 72,
                height: 48
              }
            },
            {
              label: '标题',
              prop: 'title',
              width: 200
            },
            {
              label: '简介',
              prop: 'description'
            },
            {
              label: '内容类型',
              prop: 'type',
              width: 120,
              type: 'map',
              map: that.typeMap
            },
            {
              label: '浏览人数',
              prop: 'viewCount',
              width: 100
            },
            {
              label: '状态',
              prop: 'status',
              width: 120,
              render (h, props) {
                if (props.row.status === 'PUBLISH') {
                  return (<div class='table-status-published'><span></span>已发布</div>)
                }
                if (props.row.status === 'DRAFT') {
                  return (<div class='table-status-draft'><span></span>草稿</div>)
                }
                return (<div class='table-status-removed'><span></span>已下架</div>)
              }
            },
            {
              label: '创建时间',
              prop: 'createDate',
              width: 160
            },
            {
              label: '更新时间',
              prop: 'updateDate',
              width: 160
            },
            {
              label: '创建人',
              prop: 'createBy',
              width: 100
            },
            {
              label: '操作',
              type: 'btns',
              width: 220,
              itemBind: {
                fixed: 'right'
              },
              btns (props) {
                return [
                  {
                    text: '详情',
                    type: 'text',
                    call () {
                      if (props.row.izJumpLink === '1') {
                        window.open(props.row.jumpLink)
                      } else {
                        that.self.$router.push({ path: '/staging/view', query: { id: props.row.id } })
                      }
                    }
                  },
                  {
                    text: '编辑',
                    type: 'text',
                    hide: props.row.status === 'PUBLISH',
                    call () {
                      that.self.$router.push({ path: '/staging/edit', query: { id: props.row.id } })
                    }
                  },
                  {
                    text: '下架',
                    hide: props.row.status !== 'PUBLISH',
                    type: 'text',
                    confirm: '你确定下架吗？',
                    async call () {
                      await that.$api.hermesWorkspaceNewsCancelPublish({ id: props.row.id })
                      that.$refs.table.handlerSearch()
                      that.$message.success('操作成功')
                    }
                  },
                  {
                    text: '上架',
                    type: 'text',
                    hide: props.row.status !== 'UN_PUBLISH',
                    confirm: '你确定上架吗？',
                    async call () {
                      await that.$api.hermesWorkspaceNewsPublish({ id: props.row.id })
                      that.$refs.table.handlerSearch()
                      that.$message.success('操作成功')
                    }
                  },
                  {
                    text: '删除',
                    type: 'text',
                    confirm: '你确定删除吗？',
                    hide: (props.row.status === 'PUBLISH'),
                    async call () {
                      // 此处修改为调用的的删除接口
                      await that.$api.hermesWorkspaceNewsDelete({ id: props.row.id })
                      await that.$refs.table.handlerSearch()
                      that.$message.success('操作成功')
                    }
                  }
                ]
              }
            }
          ]
        },
        async search ({
          filtersValue,
          pageFilter,
          selection,
          sort
        }) {
          // 这里可进行数据处理
          const res = await that.$api.hermesWorkspaceNewsListPage({ ...filtersValue, ...pageFilter })
          return res
        }
      }
    }
  },
  mounted () {
  }
}
</script>
<style lang="less">
.table-status-published {
  color: rgba(25, 168, 90, 1);
  span {
    display: inline-block;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background: rgba(25, 168, 90, 1);
    margin-right: 4px;
  }
}
.table-status-removed {
  color: rgba(158, 164, 178, 1);
  span {
    display: inline-block;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background: rgba(158, 164, 178, 1);
    margin-right: 4px;
  }
}
.table-status-draft {
  color: var(--main-color);
  span {
    display: inline-block;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    background: var(--main-color);
    margin-right: 4px;
  }
}
.qiankun-content-list .el-date-editor--daterange.el-input__inner {
  width: 220px;
}
</style>
