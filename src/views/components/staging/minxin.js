import Staging from './panel'
import Eidt from './content-edit.vue'
import List from './content-list.vue'
import View from './content-view.vue'
import Vue from 'vue'
const listCp = [Staging, Eidt, List, View]
const components = {}
listCp.forEach(cp => {
  components[cp.name] = cp
})
export default {
  beforeCreate () {
    window.QIANKUN_DATA.components = {
      ...window.QIANKUN_DATA.components,
      ...components,
      appendComponent (name, el, self) {
        const Profile = Vue.extend(components[name])
        const vm = new Profile({
          el: document.createElement('div'),
          propsData: {
            self: self
          }
        })
        el.append(vm.$el)
        return vm
      }
    }
  }
}
