<template>
  <div class="qiankun-iframes">
    <iframe frameborder="0" v-for="it in iframeList" :key="it.key" v-show="it.key === active" :src="it.src"></iframe>
  </div>
</template>
<script>
const mapHost = {
  'hermes-workspace': '/hermes-workspace-service/api/biLink/getByParams',
  'hera-service': 'hera-service/api/quickBI/getQuickBiUrl'
}
export default {
  data () {
    return {
      active: null,
      iframeList: []
    }
  },
  created () {
  },
  mounted () {
    window.QIANKUN_DATA.goQiankunIframe = async (pageId, filterArr, params) => {
      await this.addIframe(pageId, filterArr, params)
      let url = `/${window.QIANKUN_DATA.localPathname}#/qiankunIframe_${pageId}`
      if (params) {
        url += `_${this.getParamsStr(params)}`
      }
      this.$router.push(url)
    }
  },
  watch: {
    $route: {
      immediate: true,
      handler: function (v) {
        if (v.hash.startsWith('#/qiankunIframe_')) {
          const query = v.hash.replace('#/qiankunIframe_', '').split('?')[0]
          const keyArr = query.split('_')
          const key = keyArr[0]
          const params = this.getUrlParams(keyArr[1])
          this.active = key
          const hrefs = window.QIANKUN_DATA.user_info.menus.map(it => it.href)
          if (!hrefs.includes(`qiankunIframe_${key}`)) {
            this.$message.error('暂无权限！')
            return
          }
          this.addIframe(key, null, params)
        } else {
          this.active = null
        }
      }
    },
    iframeTabLIst: {
      immediate: true,
      handler () {
        this.deleteIframe()
      }
    }
  },
  methods: {
    getParamsStr (obj) {
      const arr = []
      for (const key in obj) {
        arr.push(`${key}=${obj[key]}`)
      }
      return arr.join('&')
    },
    getUrlParams (src) {
      const body = {}
      src && src.split('&').forEach(it => {
        const kv = it.split('=')
        body[kv[0]] = kv[1]
      })
      return body
    },
    async addIframe (key, filterArr, params) {
      const active = window.QIANKUN_DATA.active
      const hostUrl = mapHost[active]
      const iframe = this.iframeList.find(it => it.key === key)
      if (!iframe) {
        const data = await this.$api.getIframeSrcByPageId(hostUrl, key, filterArr, params)
        this.iframeList.push({
          key,
          src: data
        })
      }
      if (iframe && filterArr) {
        const data = await this.$api.getIframeSrcByPageId(hostUrl, key, filterArr, params)
        iframe.src = data
      }
      this.deleteFirstIframe()
    },
    // 根据tabList删除iframeList中的数据
    deleteIframe () {
      for (let i = 0; i < this.iframeList.length; i++) {
        const item = this.iframeList[i]
        if (!this.iframeTabLIst.includes(item.key)) {
          this.iframeList.splice(i, 1)
          i--
        }
      }
    },
    // 超过6个iframe时，删除第一个;最多缓存6个iframe
    deleteFirstIframe () {
      if (this.iframeList.length > 6) {
        this.iframeList.splice(0, 1)
      }
    }
  },

  computed: {
    // 标签页列表
    iframeTabLIst () {
      const tabList = this.$store.state.pageNav
      const iframeTabLIst = []
      tabList.forEach(item => {
        if (item.fullPath.startsWith('/qiankunIframe_')) {
          iframeTabLIst.push(item.fullPath.replace('/qiankunIframe_', ''))
        }
      })
      return iframeTabLIst
    }
  }
}
</script>

<style lang="less">
.qiankun-iframes {
  iframe {
    width: 100%;
    height: calc(100vh - 90px);
    overflow: auto;
    box-sizing: border-box;
    background-color: #ebebeb;
    white-space: initial;
  }
}
</style>
