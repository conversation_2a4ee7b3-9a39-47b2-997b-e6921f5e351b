<template>
    <div class="sy-iframe-alive-child" v-show="isIframePage">
      <component
          v-for="item in hasOpenComponentsArr"
          :key="item.name"
          :is="item.component"
          v-show="myRoute && myRoute.path === item.path"
          :isShow="myRoute && myRoute.path === item.path"
          render="qiankun"
        ></component>
    </div>
  </template>

<script>
export default {
  data () {
    return {
      hasOpenComponentsArr: [],
      isIframePage: false,
      myRoute: {}
    }
  },
  computed: {
  }
}
</script>
<style>
.sy-iframe-alive-child{
  width: 100%;
  height: calc(100vh - 88px);
  background: linear-gradient(106deg, #EAEEFF 0.38%, #F0FDFF 42.75%, #E2EBFF 85.12%);
}
</style>
