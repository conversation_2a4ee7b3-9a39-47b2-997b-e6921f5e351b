<template>
  <div class="qiankun-top-bar">
    <div class="top-bar-left">
      <div class="systems-wrap">
        <span class="navs"
          :class="{ active: 'hermes-workspace' === myActive, empty: !tenantIds || !tenantIds.includes(`hermes-workspace`) }"
          @click="() => {
            tenantIds.includes(`hermes-workspace`) && goUrl({
              label: `工作台`,
              value: `hermes-workspace`,
              admin: `空手`
            })
          }">{{ tenantIds.includes(`hermes-workspace`) ? '工作台' : '&nbsp;&nbsp;&nbsp;' }}</span>
        <template v-for="(it, i) in navs">
          <el-dropdown placement="bottom" :key="i" @visible-change="visibleChange(i, $event)"
            v-if="it.child && it.child.length" trigger="hover">
            <span class="navs" :class="{
              active: it.child.find(it => it.value === myActive),
              isHover: visibleDropdown[i],
            }" @click="goUrl(it)">{{ it.label }}<i class="el-icon--right"
                :class="`${visibleDropdown[i] ? 'el-icon-arrow-up' : 'el-icon-arrow-down'}`"></i></span>
            <el-dropdown-menu slot="dropdown" v-if="it.child" class="system-list-dropdown-wrap"
              :class="{ [`num${it.child.length}`]: true }">
              <el-dropdown-item v-for="(jt, j) in it.child" :key="j" :class="{ 'child-active': jt.value === myActive }"
                class="qiankun-topbar-modules-item" @click.native="goUrl(jt)">
                <div class="label">{{ jt.label }}</div>
                <div class="desc">{{ jt.description }}</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <span v-else :key="i" class="navs" :class="{ active: it.value === myActive }" @click="goUrl(it)">{{
            it.label }}</span>
        </template>
      </div>

    </div>
    <div class="top-bar-right">
      <div class="bar-right-content">
        <div class="env-tag-wrap">
          <div v-if="IS_GRAY" class="company-name gray">灰度环境</div>
          <div v-else-if="NODE_ENV !== 'production'" class="company-name" :class="[getStorageQiankunOrigin().env]"
            @click="openDevTools">{{
              getStorageQiankunOrigin().name }}</div>
        </div>
        <div class="tools-list-wrap">
          <div class="zeus-link" @click="openZeus">
            <img src="@/assets/logo/logo-h.png" alt="">
          </div>
          <div class="collection-wrap">
            <el-dropdown trigger="hover" @visible-change="handlerVisiableCollection">
              <span class="el-dropdown-link">
                <i class="hermes hermes-wodeshoucangjia"></i>
              </span>
              <el-dropdown-menu slot="dropdown" class="qiankun-collect-dropdown-wrap">
                <el-dropdown-item>
                  <div class="title" @click.stop>我的收藏</div>
                  <div class="menu-sys-list-wrap" @click.stop>
                    <div class="menu-sys-item" v-for="(it, i) in collectionListTree" :key="i">
                      <div class="menu-sys">
                        <span>
                          <i class="hermes expand-icon" :class="it.expand ? 'hermes-xialazhankai' : 'hermes-icon-youhua'" @click="it.expand = !it.expand"></i>
                          <span>{{ getAppnameByTenantId(it.tenantId) }}</span>
                        </span>
                        <i class="hermes hermes-dakaiquanbu" @click="openAllCollect(it.tenantId)"></i>
                      </div>
                      <div class="menu-wrap-list" v-show="it.expand">
                        <div class="mene-item" :class="{ active: getIsCollectActive(jt) }"
                          v-for="(jt, j) in it.children" :key="j" @click="tabClick(jt)">
                          <div class="text">{{ jt.name }}</div>
                          <i class="hermes hermes-shoucang" @click.stop="handlerCollect(jt)"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="open-all-sys" @click="openAllCollect()">
                    <i class="hermes hermes-dakaiquanbu"></i>
                    <span>打开全部</span>
                  </div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div class="demand-btn" @click="openDemandDrawer">
            <i class="hermes hermes-jushou"></i>
            <div class="text">我要提需求</div>
          </div>
        </div>
        <el-dropdown class="qiankun-user-info-wrap">
          <span class="qiankun-user-info">
            <el-avatar class="media-middle" size="small"
              :src="avatarUrl"></el-avatar>
            <span class="media-middle user">
              <span class="qiankun-user-name" @dblclick="closeTabNavLimit">{{ USER_INFO.user.name || "--" }}</span>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
          </span>
          <el-dropdown-menu slot="dropdown" class="qiankun-user-dropdown">
            <!-- <el-dropdown-item icon="el-icon-plus" @click.native="changePassword">修改密码</el-dropdown-item> -->

            <el-dropdown-item icon="el-icon-lock" @click.native="repassword"
              v-if="USER_INFO.user.userType === '2'">修改密码</el-dropdown-item>
            <el-dropdown-item icon="el-icon-switch-button" @click.native="logout">退出系统</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <sy-form v-bind="passwordForm" ref="passwordForm"></sy-form>
        <sy-form v-bind="devToolsBind" ref="devTools"></sy-form>
      </div>
      <demandDrawer ref="demandDrawer" v-if="USER_INFO.user.name" />
    </div>

  </div>
</template>
<script>
import demandDrawer from './demandDrawer.vue'
import utils from '@/utils'

export default {
  name: 'topBar',
  components: {
    demandDrawer
  },
  props: {
    goFeishuChart: {},
    hostMap: {},
    active: {},
    navs: { type: Array, default: () => [] }
  },
  data () {
    return {
      feishuInfo: null,
      tenantIds: [], // 所有权限租户
      visibleDropdown: [],
      scopeActive: '', // 临时active，暂无权限选中底部tabBar
      collectionListTree: []
    }
  },
  computed: {
    // 用户头像
    avatarUrl () {
      if (this.feishuInfo) {
        return this.feishuInfo.avatarUrl
      }
      return 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
    },
    myActive () {
      return this.scopeActive || this.active
    },
    USER_INFO () {
      return this.$store.state.USER_INFO
    },
    devToolsBind () {
      const that = this
      return {
        dialog: {
          width: '420px'
        },
        bind: {
          labelWidth: '90px'
        },
        list (data) {
          return [
            {
              tag: 'sy-select',
              prop: 'host',
              label: '运行环境',
              bind: { options: [{ label: '本地环境', value: 'http://localhost' }, { label: '测试环境', value: 'https://inset-testhermes.syounggroup.com' }] },
              itemBind: {
                rules: [that.$utils.RULES.RULES_REQUIRE]
              },
              on: {
                change (v) {
                  if (v === 'http://localhost') {
                    data.port = '9001'
                  }
                }
              }
            },
            {
              tag: 'el-input',
              prop: 'port',
              label: '端口号',
              hide: data.host === 'https://inset-testhermes.syounggroup.com',
              bind: {
                placeholder: '本地服务运行端口号'
              },
              itemBind: {
                rules: [that.$utils.RULES.RULES_REQUIRE]
              }
            }
          ]
        },
        async submit (data) {
          if (data.host === 'https://inset-testhermes.syounggroup.com') {
            sessionStorage.qiankunOrigin = data.host
          } else {
            sessionStorage.qiankunOrigin = `${data.host}:${data.port}`
          }
          location.reload()
        }
      }
    },
    passwordForm () {
      const that = this
      return {
        dialog: {
          width: '420px',
          showClose: false,
          showCancel: false
        },
        bind: {
          labelWidth: '90px'
        },
        list (data) {
          return [
            {
              tag: 'el-input',
              prop: 'oldPassword',
              label: '原密码',
              bind: { type: 'password' },
              itemBind: {
                rules: [that.$utils.RULES.RULES_REQUIRE]
              }
            },
            {
              tag: 'el-input',
              prop: 'newPassword',
              label: '新密码',
              bind: { type: 'password' },
              tips: { text: '密码必须由大写字母+小写字母+数字+特殊字符四种组合，字符在8~14位，且不包含花名信息，不使用旧密码。' },
              itemBind: {
                rules: [
                  that.$utils.RULES.RULES_REQUIRE,
                  {
                    validator (rule, value, callback) {
                      if (/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[~!@#$%^&*?\\.])[0-9a-zA-Z~!@#$%^&*?\\.]{8,14}$/.test(value)) {
                        callback()
                      } else {
                        callback(new Error('请输入正确格式规范'))
                      }
                    },
                    trigger: 'blur'
                  }
                ]
              }
            },
            {
              tag: 'el-input',
              prop: 'newPassword2',
              label: '确认新密码',
              bind: { type: 'password' },
              itemBind: {
                rules: [
                  that.$utils.RULES.RULES_REQUIRE,
                  {
                    trigger: 'blur',
                    validator (rule, value, callback) {
                      if (data.newPassword !== data.newPassword2) {
                        callback(new Error('两次输入密码不一致!'))
                      } else {
                        callback()
                      }
                    }
                  }]
              }
            }
          ]
        },
        async submit (data) {
          await that.$api.userModifyPwd(data)
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          that.$goLogout()
        }
      }
    },
    collectionList () {
      return this.$store.state.collectionList
    }
  },
  async created () {
    // 获取所有权限租户
    this.tenantIds = await this.$api.getTenantIdsByVirtualTenantId()
    if (localStorage.repassword) {
      localStorage.removeItem('repassword')
      this.$refs.passwordForm.open('修改密码(密码过于简单，建议修改)')
    }
  },
  methods: {
    // 隐藏功能，关闭tabNav限制
    closeTabNavLimit () {
      alert('1')
      localStorage.coloseTabNavLimit = '1'
    },
    // 打开ZEUS
    async openZeus () {
      const res = await this.$utils.axios.get('/user/api/user/createUserSession', {
        headers: {
          tenantId: 'zeus',
          extTenantId: 'defaultExtTenantId'
        }
      })
      const host = this.NODE_ENV === 'production' ? 'zeus.syounggroup.com' : 'testzeus.syounggroup.com'
      this.$utils.setCookie(`${host}.mushroom.session.id`, res.sessionId, 99999999, '.syounggroup.com')
      window.open(`https://${host}`)
    },
    // 打开全部收藏
    openAllCollect (tenantId) {
      const collectionList = tenantId ? this.collectionList.filter(it => it.tenantId === tenantId) : [...this.collectionList]
      const tabList = window.QIANKUN_DATA.pageNav
      const collectionListLength = collectionList.filter(it => !tabList.find(tab => tab.qiankunKey === it.qiankunKey)).length
      const limitNum = localStorage.coloseTabNavLimit ? 100 : 15
      if (tabList.length + collectionListLength > limitNum) {
        this.$alert('为了页面切换流畅，限制了允许打开的菜单数不超过15个，请关闭一些不用的页面', '系统提示', {
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      }
      window.QIANKUN_STORE.commit('addPageNavList', JSON.parse(JSON.stringify(collectionList)))
    },
    // 收藏夹点击
    tabClick (item) {
      utils.goSystemUrl.call(this, item.pathname, item.href)
    },
    openDemandDrawer () {
      this.$refs.demandDrawer.open()
    },
    getIsCollectActive (collect) {
      return this.collectionList.find(it => it.menuId === collect.menuId)
    },
    handlerVisiableCollection (visible) {
      if (!visible) {
        return false
      }
      const ret = []
      this.collectionList.forEach((it, index) => {
        const item = ret.find(jt => it.tenantId === jt.tenantId)
        if (!item) {
          ret.push({
            ...it,
            expand: false,
            children: [{ ...it, index }]
          })
        } else {
          item.children.push({ ...it, index })
        }
      })
      this.collectionListTree = ret
    },
    async handlerCollect (jt) {
      await this.$store.dispatch('toggleCollection', jt.menuId)
    },
    // 根据租户id获取应用名称
    getAppnameByTenantId: utils.getAppnameByTenantId,
    getStorageQiankunOrigin () {
      const qiankunOrigin = sessionStorage.qiankunOrigin
      if (!qiankunOrigin) {
        return {
          host: 'https://inset-testhermes.syounggroup.com',
          port: '',
          name: '测试环境',
          env: 'test'
        }
      }
      const url = new URL(sessionStorage.qiankunOrigin)
      return {
        host: `${url.protocol}//${url.hostname}`,
        port: url.port,
        name: url.hostname === 'localhost' ? '本地环境' : '测试环境',
        env: url.hostname === 'localhost' ? 'local' : 'test'
      }
    },
    openDevTools () {
      this.$refs.devTools.open('开发工具', this.getStorageQiankunOrigin())
    },
    // 重新设置密码
    repassword () {
      this.$refs.passwordForm.open('修改密码')
    },
    // 设置是否登录
    visibleChange (i, isShow) {
      this.$set(this.visibleDropdown, i, isShow)
    },
    async goUrl (prject) {
      const that = this
      const key = prject.value
      if (!key) {
        return false
      }
      if (prject.empty) {
        this.$message({
          message: '建设中，敬请期待。',
          type: 'warning'
        })
        return false
      }
      if (this.tenantIds.indexOf(key) === -1) {
        const match = this.hostMap[key].match(/(\/[^\\/]+)\/?$/)
        const url = `${match[1]}#/`
        this.$parent.$refs.noRootPage.openNoRoot(key, true, url)
        // const match = this.hostMap[key].match(/(\/[^\\/]+)\/?$/)
        // if (match && match[1]) {
        //   // this.$router.push(`${match[1]}#/`)
        // }
        return false
      }
      if (!this.hostMap[key]) {
        this.$message({
          message: '联系系统管理员配置域名租户映射关系！',
          type: 'warning'
        })
        console.error('联系火鸟配置域名租户映射关系！')
        return false
      }
      const match = this.hostMap[key].match(/(\/[^\\/]+)\/?$/)
      if (match && match[1]) {
        this.$router.push(`${match[1]}#/`)
      }
    },
    logout () {
      this.$goLogout()
    },
    // 修改密码
    changePassword () { },
    async getFeishuUserHeadr () {
      const data = await this.$api.getFeishuUserHeadr()
      this.feishuInfo = data
    }
  },
  mounted () {
    this.getFeishuUserHeadr()
  },
  watch: {
  }
}
</script>

<style lang="less">
.qiankun-collect-dropdown-wrap.el-dropdown-menu {
  padding-top: 0;
  box-sizing: border-box;
  border-radius: 14px;
  overflow: hidden;

  .el-dropdown-menu__item {
    background-color: #ffffff !important;
    color: #0D1B3F !important;
    padding: 0 !important;
    line-height: 16px !important;

    .title {
      display: flex;
      padding: 10px 8px;
      align-items: center;
      gap: 10px;
      align-self: stretch;
      border-bottom: 1px solid #E6E8EB;
      color: #0D1B3F;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }

    .open-all-sys {
      display: flex;
      padding: 10px 8px;
      padding-bottom: 4px;
      align-items: center;
      gap: 4px;
      color: #0D1B3F;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
      border-top: 1px solid #E6E8EB;
      i{
        font-weight: normal;
        font-size: 14px;
      }
    }

    .menu-sys-list-wrap {
      padding: 0 4px;
      max-height: 600px;
      overflow-y: auto;
      overflow-x: hidden;

      .menu-sys-item {
        width: 180px;
      }

      .menu-sys {
        display: flex;
        width: 180px;
        padding: 10px 16px 10px 4px;
        align-items: center;
        gap: 10px;
        font-weight: bold;
        justify-content: space-between;
        i{
          font-weight: normal;
          font-size: 14px;
        }
        .expand-icon {
          margin-right: 4px;
        }
      }

      .menu-wrap-list {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      .mene-item {
        display: flex;
        width: 180px;
        padding: 10px 16px 10px 32px;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        box-sizing: border-box;
        font-size: 12px;

        &:hover {
          border-radius: 8px;
          background: #F8F6FE;

          i {
            color: #9EA4B2;
            display: initial;
          }
        }

        &.active {
          border-radius: 8px;

          i {
            color: #5F3BCE;
          }
        }

        .text {
          max-width: 114px;
          line-height: 16px;
        }

        i {
          font-size: 16px;
          display: none;
        }
      }
    }
  }
}

.lw-no-root-oa {
  cursor: pointer;

  img {
    width: 18px;
    height: 18px;
    vertical-align: middle;
    color: #7f62d8;
    margin-left: 3px;
    margin-right: 1px;
  }
}

.system-list-dropdown-wrap.el-dropdown-menu {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  width: 392px;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 14px;

  &.num1,
  &.num2,
  &.num3 {
    width: 200px;
  }

  .qiankun-topbar-modules-item.el-dropdown-menu__item {
    border-radius: 8px;
    background: #F8F6FE;
    display: flex;
    width: 180px;
    box-sizing: border-box;
    padding: 8px 10px;
    align-items: flex-start;
    align-content: flex-start;
    gap: 2px;
    flex-wrap: wrap;

    &:hover {
      background: #EEF;
    }

    .label {
      color: #5F3BCE;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
    }

    .desc {
      color: #555F78;
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    &.child-active {
      background-color: #E2E1FF;
      color: #5F3BCE;
    }
  }
}

.topBar-building-popver.el-popover {
  text-align: center;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

.qiankun-top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  box-sizing: border-box;
  background-color: #fff;
  border: none;
  overflow: hidden;
  border-bottom: 1px solid #E6E8EB;

  .top-bar-left {

    align-items: center;
    margin-left: 24px;

    img {
      height: 34px;
      width: auto;
    }

  }

  .env-tag-wrap {
    margin-right: 12px;
    .company-name {
      margin-left: 10px;
      height: 26px;
      line-height: 26px;
      font-size: 14px;
      padding: 0 8px;
      background-color: #DE3509;
      color: #fff;
      border-radius: 8px;

      &.gray {
        background-color: #6F778C;
      }

      &.local {
        background-color: #409EFF;
      }
    }
  }

  .systems-wrap {
    height: 50px;
    line-height: 50px;
    overflow: hidden;
    display: flex;
    gap: 50px;
    align-items: center;
    padding-right: 18px;

    .el-dropdown {
      height: 50px;
    }

    .navs:hover,
    .navs.isHover {
      &:not(.empty) {
        color: #5F3BCE;

        .el-icon-arrow-down {
          color: #5F3BCE;
        }
      }
    }

    a,
    .navs {
      box-sizing: border-box;
      cursor: pointer;
      font-size: 14px;
      display: inline-block;
      height: 50px !important;
      color: #3E4965;
      text-align: center;
      // top: -1px;

      &.active,
      &.active:hover,
      &.active.isHover {
        .el-icon-arrow-down {
          color: #5F3BCE;
        }

        position: relative;
        color: #5F3BCE;
        font-weight: bold;
        // &::after {
        //   content: '';
        //   display: inline-block;
        //   width: 20px;
        //   height: 2px;
        //   background-color: #fff;
        //   position: absolute;
        //   top: 38px;
        //   left: 41px;
        //   border-radius: 100vw;
        // }
      }
    }
  }

  .top-bar-right {
    display: flex;
    position: relative;
    text-align: center;

    .zeus-link {
      cursor: pointer;
      border-radius: 100px;
      background: #F0F2F6;
      height: 28px;
      padding: 0 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover{
        background: #e0f2e9;
      }
      img {
        height: 18px;
        object-fit: contain;
        display: block;
      }
    }

    .help_url {
      color: #ffffff;
    }

    .bar-right-content {
      z-index: 9;
      display: flex;
      flex-flow: row;
      align-items: center;
      padding: 0 20px;
      height: 54px;
      box-sizing: border-box;

      .tools-list-wrap {
        display: flex;
        gap: 10px;
        align-items: center;

        &::after {
          content: '';
          display: inline-block;
          width: 1px;
          height: 18px;
          background-color: #E6E8EB;
        }

        &::before {
          margin-right: 6px;
          margin-left: 16px;
        }

        &::after {
          margin-left: 6px;
          margin-right: 16px;
        }

        .collection-wrap {
          i {
            color: #3E4965;
            cursor: pointer;
            display: flex;
            height: 28px;
            padding: 6px;
            justify-content: center;
            align-items: flex-start;
            gap: 10px;
            border-radius: 100px;
            background: #F0F2F6;
            line-height: 1;
            &:hover{
              color: #5F3BCE;
              background: #EEEEFF;
            }
          }
        }

        .demand-btn {
          cursor: pointer;
          border-radius: 100px;
          background: #F0F2F6;
          display: flex;
          height: 28px;
          padding: 0 10px;
          justify-content: center;
          align-items: flex-start;
          gap: 4px;
          color: #3E4965;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 28px;
          &:hover{
            color: #5F3BCE;
            background: linear-gradient(106deg, #EFECFF 0.38%, #E5EDFF 85.12%);
          }
        }
      }

      * {
        box-sizing: border-box;
      }

      .qiankun-user-info {
        top: 5px;
        display: flex;
        align-items: center;

        .media-middle {
          color: #0D1B3F;
          display: flex;
          align-items: center;

          &.el-avatar {
            width: 28px;
            height: 28px;
          }

          &.user {

            .el-icon-arrow-down {
              vertical-align: text-top;
              color: #0D1B3F;
              font-size: 14px;
              font-weight: bold;
              margin-left: 8px;
            }
          }

          .qiankun-user-name {
            display: inline-block;
            text-align: left;
            margin-left: 8px;
            width: 30px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            font-size: 14px;
            line-height: 14px;
          }
        }
      }
    }

    .qiankun-user-info-wrap {
      width: 95px;
      display: flex;
      align-items: center;
    }

  }
}

.qiankun-user-dropdown {
  .el-icon-lock {
    color: inherit;
    margin-left: 0px;
  }
}

</style>
