<template>
  <div class="qiankun-preview" v-if="showPreview">
    <el-image-viewer
      v-if="showType === 'image'"
      :urlList="image.previewImages"
      :on-close="closeViewer"
    ></el-image-viewer>
    <div v-if="showType === 'excel'" class="qiankun-preview-luckysheet">
      <div
        id="qiankun-preview-luckysheet"
        class="qiankun-preview-luckysheet"
      ></div>
      <button
        class="qiankun-preview-luckysheet-btn"
        @click="downloadLuckysheet"
      >
        下载文件<i class="el-icon-download el-icon--right"></i>
      </button>
    </div>
    <div class="qiankun-preview-word" v-if="showType === 'word'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div ref="word"></div>
    </div>
    <div class="qiankun-preview-video" v-if="showType === 'video'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div id="qiankun-preview-video"></div>
    </div>
    <div class="qiankun-preview-audio" v-if="showType === 'audio'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div class="qiankun-preview-audio-wrap">
        <div class="canvas" ref="qiankun-preview-audio"></div>
        <div class="control">
          <i class="audio-play el-icon-arrow-left" @click="skipBackward"></i>
          <i
            class="audio-play"
            @click="audioPlay"
            :class="`${
              audio.isplay ? 'el-icon-video-pause' : 'el-icon-video-play'
            }`"
          ></i>
          <i class="audio-play el-icon-arrow-right" @click="skipForward"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import resourceManager from '@/utils/resourceManager'
import axios from 'axios'

const docx = require('docx-preview')
const FILE_TYPE = {
  pdf: ['pdf'],
  image: ['png', 'jpg', 'jpeg', 'gif'],
  excel: ['xlsx'],
  word: ['docx'],
  video: ['mp4', 'webm', 'ogv', 'm3u8', 'flv'],
  audio: ['ogg', 'mp3']
}

const EXCEL_CONFIG = {
  container: 'qiankun-preview-luckysheet',
  showinfobar: false,
  showsheetbar: false,
  showstatisticBar: false,
  enableAddRow: false,
  enableAddCol: false,
  sheetFormulaBar: false,
  allowEdit: false
}

export default {
  components: {
    ElImageViewer
  },
  data () {
    return {
      showPreview: false,
      showType: '',
      image: {
        previewImages: []
      },
      excel: {
        url: ''
      },
      video: {
        url: ''
      },
      audio: {
        url: '',
        isplay: false
      },
      // 当前预览实例ID
      currentInstanceId: null
    }
  },
  methods: {
    // 下载excel
    downloadLuckysheet () {
      window.open(this.excel.url)
    },

    // 获取文件类型
    getType (data) {
      if (typeof data === 'string') {
        const suffix = this.getUrlSuffix(data)
        for (const key in FILE_TYPE) {
          if (FILE_TYPE[key].indexOf(suffix) !== -1) {
            return key
          }
        }
      }
    },

    // 获取文件后缀名
    getUrlSuffix (url) {
      const arr = url.split('.')
      return arr[arr.length - 1].toLowerCase()
    },

    $preview (data, type) {
      if (!type) {
        type = this.getType(data)
      }
      if (type === 'excel') {
        window.open(`${window.QIANKUN_DATA.origin}/preview?src=${encodeURI(data)}`)
      } else {
        this.preview(data, type)
      }
    },

    async preview (data, type) {
      // 先清理之前的预览
      this.closeViewer()

      this.showPreview = true
      if (!type) {
        type = this.getType(data)
      }
      this.showType = type
      this.currentInstanceId = `preview_${Date.now()}`

      // 支持服务端接口文件预览
      if (typeof data === 'object' && !Array.isArray(data)) {
        data = window.URL.createObjectURL(data)
      }

      try {
        await this.$nextTick()

        switch (type) {
          case 'image':
            this.previewImage(data)
            break
          case 'excel':
            await this.previewExcel(data)
            break
          case 'word':
            await this.previewWord(data)
            break
          case 'video':
            await this.previewVideo(data)
            break
          case 'audio':
            await this.previewAudio(data)
            break
          default:
            window.open(data)
        }
      } catch (error) {
        console.error('预览文件失败:', error)
        this.$message.error('预览文件失败')
        this.closeViewer()
      }
    },

    previewImage (data) {
      if (!Array.isArray(data)) {
        data = [data]
      }
      this.image.previewImages = data
    },

    async previewExcel (url) {
      this.excel.url = url
      const name = this.getFileName(url)

      await resourceManager.createExcelViewer({
        url,
        name,
        options: EXCEL_CONFIG
      }, this.currentInstanceId)
    },

    async previewWord (url) {
      try {
        const { data } = await axios({
          method: 'get',
          responseType: 'blob',
          url
        })
        await docx.renderAsync(data, this.$refs.word)
      } catch (error) {
        console.error('预览Word文档失败:', error)
        throw error
      }
    },

    async previewVideo (url) {
      this.video.url = url

      const player = await resourceManager.createVideoPlayer({
        id: 'qiankun-preview-video',
        url
      }, this.currentInstanceId)

      return player
    },

    async previewAudio (url) {
      this.audio.url = url

      const wavesurfer = await resourceManager.createAudioPlayer({
        container: this.$refs['qiankun-preview-audio'],
        barWidth: 2,
        waveColor: '#5F3BCE',
        progressColor: '#ebebeb',
        barHeight: 1,
        barGap: null
      }, this.currentInstanceId)

      wavesurfer.load(url)

      // 监听播放状态变化
      wavesurfer.on('play', () => {
        this.audio.isplay = true
      })

      wavesurfer.on('pause', () => {
        this.audio.isplay = false
      })

      return wavesurfer
    },

    // 音频控制方法
    skipBackward () {
      const wavesurfer = resourceManager.getInstance('audio', this.currentInstanceId)
      if (wavesurfer) {
        wavesurfer.skipBackward()
      }
    },

    skipForward () {
      const wavesurfer = resourceManager.getInstance('audio', this.currentInstanceId)
      if (wavesurfer) {
        wavesurfer.skipForward()
      }
    },

    audioPlay () {
      const wavesurfer = resourceManager.getInstance('audio', this.currentInstanceId)
      if (wavesurfer) {
        wavesurfer.playPause()
        this.audio.isplay = wavesurfer.isPlaying()
      }
    },

    closeViewer () {
      this.showPreview = false

      // 清理当前预览的所有资源
      if (this.currentInstanceId) {
        resourceManager.destroyVideoPlayer(this.currentInstanceId)
        resourceManager.destroyAudioPlayer(this.currentInstanceId)
        resourceManager.destroyExcelViewer(this.currentInstanceId)
        this.currentInstanceId = null
      }

      // 重置状态
      this.showType = ''
      this.audio.isplay = false
      this.image.previewImages = []
    },

    getFileName (url) {
      const arr = url.split('/')
      return arr[arr.length - 1]
    }
  },

  beforeDestroy () {
    // 组件销毁时清理所有资源
    this.closeViewer()
  }
}
</script>

<style scoped>
/* 样式保持不变 */
</style>
