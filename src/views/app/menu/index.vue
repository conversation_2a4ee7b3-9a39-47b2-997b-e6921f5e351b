<script>
export default {
  name: 'qiankun-menu',
  props: {
    collapse: {
      type: Boolean
    },
    hasSearch: {
      type: Boolean,
      default: true
    },
    isOpen: {
      default: true
    }
  },
  data () {
    return {
      textTips: {
        text: '',
        top: -100
      },
      activePath: '',
      suggestions: ''
    }
  },
  computed: {
    menus () {
      return this.$store.state.USER_INFO.menus
    },
    // 过滤不显示和非路由后的菜单
    NavMenus () {
      return this.menus.filter((it) => it.isShow !== '0' && it.type === 'router')
    },
    // 所有导航 没有权限的则没有菜单
    menuMap () {
      const navs = JSON.parse(JSON.stringify(this.NavMenus))
      // 动态生成路由
      const map = {}
      for (let i = 0; i < navs.length; i++) {
        let it = navs[i]
        if (it.isShow === '0') { // 过滤不可见菜单
          continue
        }
        const pid = it.parentId
        // 去重
        if (map[it.id] && map[it.id].name) {
          continue
        }
        if (!it.children) {
          it.children = []
        }
        if (!map[pid]) {
          map[pid] = {
            children: []
          }
        }
        it = { ...it, ...map[it.id] }

        map[pid].children.push(it)
        // 排序
        map[pid].children = map[pid].children.sort((a, b) => a.sort - b.sort)
        map[it.id] = it
      }
      return map
    },
    menu () {
      if (!this.menuMap[0]) {
        return []
      }
      this.menuMap[0].children.forEach((it) => {
        if (!it.icon) {
          it.icon = 'hermes-xitongguanli1'
        }
      })
      return this.menuMap[0].children
    },
    activeMenuPath () {
      return `${this.$route.path}${this.$route.hash}`
    }
  },
  watch: {
    isOpen (v) {
      this.collapse = !v
    },
    menu (v) { // 菜单更新时更新active，避免菜单未更新路由已更新导致菜单选中错误
      this.$nextTick(() => {
        this.$refs.menu.updateActiveIndex(this.activeMenuPath)
      })
    }
  },
  methods: {
    openMenu () {
      this.$emit('update:collapse', false)
    },
    dealUrl (url) {
      if (!url) {
        return ''
      }
      if (url.startsWith('/_blank_')) {
        return url
      }
      if (url.startsWith(`/${window.QIANKUN_DATA.localPathname}#/`)) {
        return url
      }
      return `/${window.QIANKUN_DATA.localPathname}#/${url.replace(/^\//, '')}`
    },
    hanlderSelect (index, indexPath) {
      if (/^\/_blank_/.test(index)) {
        window.open(index.slice(8))
        this.$refs.menu.activeIndex = `/${window.QIANKUN_DATA.localPathname}${this.$route.hash}`
        // 解决打开外链时focus的问题
        const el = this.$refs.autocomplete.$el.querySelector('input')
        el.focus()
        el.blur()
        return false
      }
      this.$router.push(`${index}`)
      setTimeout(() => {
        this.$refs.menu.activeIndex = `/${window.QIANKUN_DATA.localPathname}${this.$route.hash}`
      }, 0)
    },
    // 渲染图标
    renderIcon (menu) {
      if (!menu.icon) {
        return false
      }
      if (/^svg-/.test(menu.icon)) {
        return <sy-svg icon-class={menu.icon.slice(4)} />
      }
      if (menu.icon && typeof menu.icon === 'string' && menu.icon.startsWith('el-')) {
        return <i class={`menu-icon ${menu.icon}`}></i>
      }
      return <i class={`menu-icon hermes ${menu.icon}`}></i>
    },
    getItems (menu) {
      if (menu.isShow === '0') {
        return false
      }
      const children =
        menu.children &&
        menu.children.filter((it) => it.isShow !== '0' && it.type === 'router')
      if (children && children.length) {
        return (
          <el-submenu
            popper-class="qiankun-menu-popup"
            index={this.dealUrl(menu.href || menu.name)}
            key={menu.qiankunKey + menu.name}
          >
            <template slot="title">
              {this.renderIcon(menu)}
              <span class="submenu-text menuitem-text-sub" slot="title">
                {this.textTipsNode(menu.name)}
              </span>
            </template>
            {children.map((it) => this.getItems(it))}
          </el-submenu>
        )
      }

      return (
        <el-menu-item
          index={this.dealUrl(menu.href)}
          key={menu.qiankunKey || menu.code || menu.href}
        >
          {this.renderIcon(menu)}
          <span slot="title" class="menuitem-text-item">
            {this.textTipsNode(menu.name)}
          </span>
        </el-menu-item>
      )
    },
    textTipsNode (text) {
      return (
        <span
          on-mouseover={(e) => this.navEnter(e, text)}
          on-mouseout={(e) => this.navOut(e, text)}
        >
          {text}
        </span>
      )
    },
    // 鼠标进入
    navEnter (e, text) {
      if (this.collapse) {
        return false
      }
      const el = e.currentTarget
      const pel = el.parentNode
      if (pel.scrollWidth > pel.offsetWidth) {
        this.textTips.top = e.currentTarget.getBoundingClientRect().y - 9
        this.textTips.text = text
      }
    },
    navOut (e) {
      this.textTips.top = -100
      this.textTips.text = ''
    },
    // 搜索菜单
    fetchSuggestions (queryString, call) {
      if (!queryString.trim()) {
        call([])
        return false
      }
      const arr = this.NavMenus
        .map((it) => ({ // 映射
          label: it.name,
          value: this.dealUrl(it.href)
        })).filter((it) => it.value && it.label.indexOf(queryString.trim()) !== -1)
      call(arr)
    },
    // 搜索菜单选择
    handleSelect (item) {
      this.hanlderSelect(item.value)
      this.suggestions = ''
    },
    // 搜索菜单输入
    handlerSuggestions (v) {
      this.suggestions = v || ''
    }
  },
  created () { },
  render () {
    const { hasSearch } = this
    return (
      <div class={{ collapse: this.collapse, 'qiankun-menu': true }}>
        {
          hasSearch && <el-autocomplete
            ref="autocomplete"
            class="inline-input"
            size="mini"
            prefix-icon="el-icon-search"
            placeholder="搜索菜单"
            value={this.suggestions}
            on-input={this.handlerSuggestions}
            on-select={this.handleSelect}
            fetch-suggestions={this.fetchSuggestions}
            scopedSlots={{ default: (item) => item.item.label }}
          ></el-autocomplete>
        }
        <i v-if="collapse" class="menu-search-icon el-icon-search" on-click={this.openMenu}></i>
        <el-menu
          ref="menu"
          default-active={this.activeMenuPath}
          router={false}
          collapse={this.collapse}
          collapse-transition={false}
          on-select={this.hanlderSelect}
          unique-opened
        >
          {this.menu.map((it) => this.getItems(it))}
        </el-menu>
        <div class="textTips" style={{ top: `${this.textTips.top}px` }}>
          {this.textTips.text}
        </div>
      </div>
    )
  }
}
</script>

<style lang="less">
.qiankun-menu-popup {
  .el-menu {
    background-color: #fff;

    .el-submenu__title {
      color: #0D1B3F;
    }

    .el-submenu__title:not(.is-disabled):hover,
    .el-menu-item:not(.is-disabled):hover,
    .el-submenu.is-opened .el-submenu__title {
      background-color: #EFEBFA;
      color: #5F3BCE;

      i {
        color: #5F3BCE;
      }
    }

    .el-menu-item:not(.is-disabled).is-active,
    .el-submenu:not(.is-disabled).is-active .el-submenu__title {
      background-color: #5F3BCE;
      color: #fff;

      i {
        color: #fff;
      }
    }

    .el-menu-item {
      color: #0D1B3F;
    }
  }
}

.qiankun-menu {
  flex: 0;
  flex-shrink: 0;
  flex-basis: 180px;
  overflow-y: hidden;
  overflow-x: hidden;
  display: inline-block;
  z-index: 2000;
  width: 180px;
  box-sizing: border-box;

  // 默认值，避免focus覆盖
  .el-menu .el-menu-item:not(.is-disabled):focus {
    background-color: initial;
    color: #909399;

    i {
      color: #909399;
    }
  }

  .el-menu .el-menu-item:not(.is-active):hover,
  .el-menu .el-submenu__title:not(.is-active):hover {
    background-color: #353A3F;
    color: #fff;

    i {
      color: #fff;
    }
  }

  .el-menu .el-menu-item:not(.is-disabled).is-active,
  .el-menu .el-submenu__title.is-active,
  .el-menu.el-menu--collapse .el-submenu.is-active>.el-submenu__title {
    background-color: #5F3BCE;
    color: #fff;

    i {
      color: #fff;
    }
  }

  color: #0D1B3F;

  .el-menu--collapse {
    width: 44px;
  }

  .el-autocomplete {
    width: auto;
    margin: 4px 12px;

    .el-input--mini .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }

  .el-menu--collapse {
    .el-submenu.is-active {
      background-color: #5F3BCE;

      .el-submenu__title {
        color: #fff;

        i {
          color: #fff;
        }
      }
    }
  }

  &.collapse {
    flex-basis: 42px;

    >.el-autocomplete {
      display: none;
    }

    .menu-search-icon {
      color: #909399;
      font-size: 18px;
      width: 100%;
      padding-left: 12px;
      padding-top: 9px;
      padding-bottom: 9px;
      cursor: pointer;

      &:hover {
        color: #fff;
      }
    }

  }

  .el-menu {
    border-right: 0;

    .menuitem-text-sub,
    .menuitem-text-item {
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 120px;

      .el-tooltip {
        display: inline-block;
        width: calc(100% + 20px);
      }
    }

    .menuitem-text-sub {
      width: 110px;
    }

    .menuitem-text-item {
      width: 126px;
    }

    .el-menu .menuitem-text-item {
      width: 124px;
    }

    .el-menu .el-menu .menuitem-text-item {
      width: 108px;
    }

    .el-menu .menuitem-text-sub {
      width: 102px;
    }

    .el-submenu__title .menu-icon,
    .el-menu-item .menu-icon {
      font-size: 18px;
    }
  }

  .textTips {
    position: fixed;
    left: 186px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    word-wrap: break-word;
    z-index: 10000;

    &::before {
      left: -6px;
      top: 12px;
      position: absolute;
      content: "";
      display: inline-block;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-right: 6px solid rgba(0, 0, 0, 0.8);
      ;
      border-bottom: 6px solid transparent;
    }
  }
}
</style>
