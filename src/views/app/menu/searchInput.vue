<template>
    <div class="qiankun-search-menu" v-if="myMenus.length" :class="{collapse}">
        <el-popover :popper-class="`qiankun-search-menu-input-popver ${!searchMenus.length && 'hide'}`"  placement="right-start" title="搜索结果"
            width="auto"  trigger="focus">
            <el-input slot="reference" ref="input" class="search-menu" size="mini"
                prefix-icon="el-icon-search" placeholder="搜索菜单" :value="suggestions" @input="handlerSuggestions"
                @click.native="goSearch"
                >
            </el-input>
            <div class="qiankun-menu-child">
                <div class="child-menu" v-for="(menu, i) in searchMenus" :key="i" @click="goMenuUrl(menu.href)" :class="{active:getActive(menu)}">
                    <div class="text">{{ menu.name }}</div>
                    <i class="hermes hermes-shoucang" :class="{active:getIsCollectActive(menu)}" @click.stop="handlerCollect(menu)"></i>
                </div>
            </div>
        </el-popover>

    </div>
</template>

<script>
export default {
  props: {
    myMenus: {},
    collapse: {},
    NavMenus: {}
  },
  data () {
    return {
      suggestions: ''
    }
  },
  computed: {
    searchMenus () {
      const queryString = this.suggestions.trim()
      if (!queryString) {
        return []
      }
      return this.NavMenus.filter((it) => it.href && it.name && it.name.indexOf(queryString) !== -1)
    },
    collectionList () {
      return this.$store.state.collectionList
    }
  },
  methods: {
    async handlerCollect (menu) {
      await this.$store.dispatch('toggleCollection', menu.id)
    },
    getIsCollectActive (menu) {
      return this.collectionList.find(it => it.menuId === menu.id)
    },
    goSearch () {
      if (!this.collapse) {
        return
      }
      this.$emit('setCollapse', false)
      setTimeout(this.$refs.input.focus, 200)
    },
    handlerSuggestions (v) {
      this.suggestions = v || ''
    },
    // 搜索菜单选择
    handleSearchSelect (item) {
      this.goMenuUrl(item.value)
      this.suggestions = ''
    },
    goMenuUrl (href) {
      this.$emit('goMenuUrl', href)
    },
    getActive (it) {
      if (it.href) {
        return it.href.replace(/^\//, '') === this.$route.hash.slice(1).replace(/^\//, '')
      }
      return false
    }
  }
}
</script>

<style lang="less">
.qiankun-search-menu {

  .search-menu {
    transition: all 0.1s ease-in-out;
    margin: 20px 0px 0;
    box-sizing: border-box;
    height: 40px;
    font-size: 12px;
    border-radius: 8px;
    text-align: center;
    background-color: #fff;
    width: 110px;
    cursor: pointer;
    color: #5A5F8C;
    &:hover{
      outline: 1px solid #5F3BCE;
    }
    .el-input__inner{
      color: #5A5F8C;
      height: 40px;
      box-sizing: border-box;
      border-radius: 100px;
      background-color: transparent;
      border:none;
      font-size: 12px;
    }
    .el-input__icon{
      color: #5A5F8C;
      &::before{
        line-height: 40px;
        font-size: 16px;
      }
    }
    .el-input--mini .el-input__inner {
      height: 28px;
      line-height: 28px;
    }
  }

  &.collapse{
    .search-menu{
      width: 40px;
      border-radius: 100px;
      &:hover{
        outline: none;
        background-color: rgba(0, 0, 0, 0.1);
      }
      .el-input__inner{
        width: 40px;
        display: none;
        padding: 0 15px;
      }
      .el-input__prefix{
        left: 7px;
      }
    }
  }
}

.el-popper.qiankun-search-menu-input-popver {
    border-radius: 14px;
    padding: 0;
    &.hide{
        display: none;
    }
    .el-popover__title {
        display: flex;
        padding: 10px 8px;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        border-bottom: 1px solid #E6E8EB;
        color: #0D1B3F;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        margin-bottom: 0;
    }

    .qiankun-menu-child {
        padding: 4px;
        max-height: 600px;
        overflow: auto;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 4px 8px;

        .child-menu-wrap {
            .child-menu-list {
                display: flex;
                flex-direction: column;
                gap: 2px;
            }
        }

        .child-menu {
            display: flex;
            width: 180px;
            box-sizing: border-box;
            padding: 10px 16px 10px 16px;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            border-radius: 8px;
            font-size: 12px;
            line-height: 14px;

            &.active,
            &:hover.active {
                background: #DEDDFF;
            }

            &:hover {
                background: #F8F6FE;

                i {
                    display: initial;
                }
            }

            .text {
                max-width: 116px;
                line-height: 16px;
            }

            i {
                display: none;
                font-size: 14px;
                color: rgba(158, 164, 178, 1);

                &.active {
                    color: #5F3BCE;
                }
            }
        }

        .child-menu-tree {
            .title {
                display: flex;
                padding: 10px 16px;
                color: #0D1B3F;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
            }

            .child-menu {
                padding-left: 32px;
            }
        }
    }
}
</style>
