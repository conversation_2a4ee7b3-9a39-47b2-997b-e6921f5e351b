<template>
  <div class="qiankun-new-menu" :class="{ collapse }">
    <img class="logo" v-show="collapse" src="https://oss.syounggroup.com/bigfile/defaultTenantId/hermes-logo1.png" alt="">
    <img class="logo" v-show="!collapse" src="https://oss.syounggroup.com/bigfile/defaultTenantId/hermes-logo2.png"
      alt="">
    <searchInput :myMenus="myMenus" :NavMenus="NavMenus" :collapse="collapse" @setCollapse="$emit('update:collapse',$event)"  @goMenuUrl="goMenuUrl"/>
    <div class="menus-wrap">
      <div v-for="(it) in myMenus" :key="it.code" :class="{active:getActive(it)}">
        <div v-if="it.href" class="menu" @click="goMenuUrl(it.href)">
          <i :class="['menu-icon', 'hermes', it.icon]"></i>
          <div class="text">{{ it.name }}</div>
        </div>
        <el-popover v-else placement="right" :title="it.name" width="auto" trigger="hover" popper-class="qiankun-child-menu-popover" :open-delay="100">
          <div slot="reference" class="menu">
            <i :class="['menu-icon', 'hermes', it.icon]"></i>
            <div class="text">{{ it.name }}</div>
          </div>
          <childMenu :data="it" @goMenuUrl="goMenuUrl"/>
        </el-popover>
      </div>
    </div>
  </div>
</template>

<script>
import utils from '@/utils'
import childMenu from './childMenu.vue'
import searchInput from './searchInput.vue'
export default {
  components: {
    childMenu,
    searchInput
  },
  props: {
    collapse: {

    }
  },
  data () {
    return {
      popover: {
        visible: false
      },
      suggestions: ''
    }
  },
  computed: {
    logoSrc () {
      if (this.collapse) {
        return 'https://oss.syounggroup.com/bigfile/defaultTenantId/收起logo.png'
      }
      return 'https://oss.syounggroup.com/bigfile/defaultTenantId/logo-expand.png'
    },
    menus () {
      return this.$store.state.USER_INFO.menus
    },
    myMenus () {
      if (!this.menuMap[0]) {
        return []
      }
      this.menuMap[0].children.forEach((it) => {
        if (!it.icon) {
          it.icon = 'hermes-a-shuju321'
        }
      })
      return this.menuMap[0].children
    },
    // 过滤不显示和非路由后的菜单
    NavMenus () {
      return this.menus.filter((it) => it.isShow !== '0' && it.type === 'router')
    },
    // 所有导航 没有权限的则没有菜单
    menuMap () {
      const navs = JSON.parse(JSON.stringify(this.NavMenus))
      // 动态生成路由
      const map = {}
      for (let i = 0; i < navs.length; i++) {
        let it = navs[i]
        if (it.isShow === '0') { // 过滤不可见菜单
          continue
        }
        const pid = it.parentId
        // 去重
        if (map[it.id] && map[it.id].name) {
          continue
        }
        if (!it.children) {
          it.children = []
        }
        if (!map[pid]) {
          map[pid] = {
            children: []
          }
        }
        it = { ...it, ...map[it.id] }

        map[pid].children.push(it)
        // 排序
        map[pid].children = map[pid].children.sort((a, b) => a.sort - b.sort)
        map[it.id] = it
      }
      return map
    }
  },
  methods: {
    goSearch () {
      this.$emit('update:collapse', false)
      setTimeout(() => {
        this.$refs.autocomplete.focus()
      }, 100)
    },
    getActive (it) {
      if (it.href) {
        return it.href.replace(/^\//, '') === this.$route.hash.slice(1).replace(/^\//, '')
      }
      for (let i = 0; i < it.children.length; i++) {
        const ret = this.getActive(it.children[i])
        if (ret) {
          return true
        }
      }
      return false
    },

    goMenuUrl: utils.goMenuUrl
  }
}
</script>

<style lang="less">
.qiankun-new-menu {
  box-sizing: border-box;
  flex: 0 0 120px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2000;

  .menu{
    transition: all 0.1s ease-in-out;
  }

  &.collapse {
    flex: 0 0 80px;
    width: 48px;

    .menus-wrap {
      .menu {
        box-sizing: border-box;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 54px;
        width: 72px;
        overflow: hidden;
        gap: 6px;
        padding: 0;
        border-radius: 14px;

        .menu-icon {
          margin-right: 0px;
        }

        .text {
          font-size: 12px;
          max-width: 60px;
        }
      }
    }
  }

  .logo {
    width: 64px;
    margin-top: 22px;
  }

  .menus-wrap {
    margin-top: 30px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0px;
    height: calc(100vh - 180px);
    overflow: auto;
    &::-webkit-scrollbar-thumb{
      background-color: rgba(0,0,0,0) !important;
      border-radius:12px !important;
    }
    &::-webkit-scrollbar {
      width: 4px !important;
      height: 4px !important;
    }
    &:hover::-webkit-scrollbar-thumb{
      background-color: rgba(0,0,0,0.1) !important;
    }

    .active{
      .menu {
        background-color: rgba(255, 255, 255, 0.60);
        i{
          color:rgba(95, 59, 206, 1) ;
        }
      }
    }
    .menu {
      display: flex;
      height: 40px;
      align-items: center;
      color: #5A5F8C;
      cursor: pointer;
      border-radius: 8px;
      padding: 12px 10px;
      box-sizing: border-box;
      width: 110px;
      overflow: hidden;

      &:hover {
        background-color: rgba(255,255,255,0.6);
      }

      .menu-icon {
        margin-right: 4px;
        font-size: 18px;
        line-height: 18px;
      }

      .text {
        font-size: 12px;
        line-height: 16px;
        max-width: 90px;
        overflow: hidden;
      }
    }
  }

}
</style>
