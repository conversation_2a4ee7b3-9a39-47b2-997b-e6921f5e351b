<script>
export default {
  name: 'qiankun-menu',
  props: {
    hasSearch: {
      type: Boolean,
      default: true
    },
    menus: {
      type: Array
    },
    vm: { // 主框架VM实例
      type: Object
    },
    childVm: { // 主框架VM实例
      type: Object
    },
    isOpen: {
      default: true
    }
  },
  data () {
    return {
      collapse: false,
      textTips: {
        text: '',
        top: -100
      },
      activePath: '',
      suggestions: ''
    }
  },
  computed: {
    // 过滤不显示和非路由后的菜单
    NavMenus () {
      return this.menus.filter((it) => it.isShow !== '0' && it.type === 'router')
    },
    // 所有导航 没有权限的则没有菜单
    menuMap () {
      const navs = JSON.parse(JSON.stringify(this.NavMenus))
      // 动态生成路由
      const map = {}
      for (let i = 0; i < navs.length; i++) {
        let it = navs[i]
        if (it.isShow === '0') { // 过滤不可见菜单
          continue
        }
        const pid = it.parentId
        // 去重
        if (map[it.id] && map[it.id].name) {
          continue
        }
        if (!it.children) {
          it.children = []
        }
        if (!map[pid]) {
          map[pid] = {
            children: []
          }
        }
        it = { ...it, ...map[it.id] }

        map[pid].children.push(it)
        // 排序
        map[pid].children = map[pid].children.sort((a, b) => a.sort - b.sort)
        map[it.id] = it
      }
      return map
    },
    menu () {
      this.menuMap[0].children.forEach((it) => {
        if (!it.icon) {
          it.icon = 'hermes-xitongguanli1'
        }
      })
      return this.menuMap[0].children
    },
    activeMenuPath () {
      if (this.childVm) {
        return this.dealUrl(this.childVm.$route.path)
      }
      return this.dealUrl(this.vm.$route.path)
    }
  },
  methods: {
    dealUrl (url) {
      return `/${url.replace(/^\//, '')}`
    },
    hanlderSelect (index, indexPath) {
      if (/^\/_blank_/.test(index)) {
        window.open(index.slice(8))
        this.$refs.menu.activeIndex = this.vm.$route.path
        // 解决打开外链时focus的问题
        const el = this.$refs.autocomplete.$el.querySelector('input')
        el.focus()
        el.blur()
        return false
      }
      this.vm.$router.push(index)
    },
    // 渲染图标
    renderIcon (menu) {
      if (!menu.icon) {
        return false
      }
      if (/^svg-/.test(menu.icon)) {
        return <sy-svg icon-class={menu.icon.slice(4)} />
      }
      if (menu.icon && typeof menu.icon === 'string' && menu.icon.startsWith('el-')) {
        return <i class={`menu-icon ${menu.icon}`}></i>
      }
      return <i class={`menu-icon hermes ${menu.icon}`}></i>
    },
    getItems (menu) {
      if (menu.isShow === '0') {
        return false
      }
      const children =
        menu.children &&
        menu.children.filter((it) => it.isShow !== '0' && it.type === 'router')
      if (children && children.length) {
        return (
          <el-submenu
            index={this.dealUrl(menu.href || menu.name)}
            key={menu.code || menu.href || menu.name}
          >
            <template slot="title">
              {this.renderIcon(menu)}
              <span class="submenu-text menuitem-text-sub" slot="title">
                {this.textTipsNode(menu.name)}
              </span>
            </template>
            {children.map((it) => this.getItems(it))}
          </el-submenu>
        )
      }

      return (
        <el-menu-item
          index={this.dealUrl(menu.href)}
          key={menu.code || menu.href}
        >
          {this.renderIcon(menu)}
          <span slot="title" class="menuitem-text-item">
            {this.textTipsNode(menu.name)}
          </span>
        </el-menu-item>
      )
    },
    textTipsNode (text) {
      return (
        <span
          on-mouseover={(e) => this.navEnter(e, text)}
          on-mouseout={(e) => this.navOut(e, text)}
        >
          {text}
        </span>
      )
    },
    // 鼠标进入
    navEnter (e, text) {
      if (this.collapse) {
        return false
      }
      const el = e.currentTarget
      const pel = el.parentNode
      if (pel.scrollWidth > pel.offsetWidth) {
        this.textTips.top = e.currentTarget.getBoundingClientRect().y - 9
        this.textTips.text = text
      }
    },
    navOut (e) {
      this.textTips.top = -100
      this.textTips.text = ''
    },
    // 搜索菜单
    fetchSuggestions (queryString, call) {
      if (!queryString.trim()) {
        call([])
        return false
      }
      const arr = this.NavMenus
        .map((it) => ({ // 映射
          label: it.name,
          value: it.href
        })).filter((it) => it.value && it.label.indexOf(queryString.trim()) !== -1)
      call(arr)
    },
    // 搜索菜单选择
    handleSelect (item) {
      this.hanlderSelect(item.value)
      this.suggestions = ''
    },
    // 搜索菜单输入
    handlerSuggestions (v) {
      this.suggestions = v || ''
    }
  },
  created () {},
  watch: {
    isOpen (v) {
      this.collapse = !v
    }
  },
  render () {
    const { hasSearch } = this
    return (
      <div class={{ collapse: this.collapse, 'qiankun-menu': true }}>
        {
          hasSearch && <el-autocomplete
            ref="autocomplete"
            class="inline-input"
            size="mini"
            prefix-icon="el-icon-search"
            placeholder="搜索菜单"
            value={this.suggestions}
            on-input={this.handlerSuggestions}
            on-select={this.handleSelect}
            fetch-suggestions={this.fetchSuggestions}
            scopedSlots={{ default: (item) => item.item.label }}
          ></el-autocomplete>
        }
        <el-menu
          ref="menu"
          default-active={this.activeMenuPath}
          router={false}
          collapse={this.collapse}
          collapse-transition={false}
          on-select={this.hanlderSelect}
          unique-opened
        >
          {this.menu.map((it) => this.getItems(it))}
        </el-menu>
        <div class="textTips" style={{ top: `${this.textTips.top}px` }}>
          {this.textTips.text}
        </div>
      </div>
    )
  }
}
</script>

<style lang="less">
.qiankun-menu {
  flex: 0;
  flex-shrink: 0;
  flex-basis: 180px;
  overflow-y: hidden;
  overflow-x: hidden;
  display: inline-block;
  z-index: 2000;
  width: 180px;
  box-sizing: border-box;
  .el-autocomplete {
    width: 90%;
    margin: 4px 0 4px 5%;
  }
  &.collapse {
    flex-basis: 42px;
    > .el-autocomplete {
      display: none;
    }
  }
  .el-menu {
    border-right: 0;
    .menuitem-text-sub,
    .menuitem-text-item {
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 120px;
      .el-tooltip {
        display: inline-block;
        width: calc(100% + 20px);
      }
    }
    .menuitem-text-sub {
      width: 110px;
    }
    .menuitem-text-item {
      width: 126px;
    }
    .el-menu .menuitem-text-item {
      width: 124px;
    }
    .el-menu .el-menu .menuitem-text-item {
      width: 108px;
    }
    .el-menu .menuitem-text-sub {
      width: 102px;
    }
    .el-submenu__title .menu-icon,.el-menu-item .menu-icon{
      font-size: 18px;
    }
  }
  .textTips {
    position: fixed;
    left: 186px;
    background: rgba(0,0,0,0.8);
    color: #fff;
    border-radius: 4px;
    padding: 10px;
    font-size: 12px;
    word-wrap: break-word;
    z-index: 10000;
    &::before {
      left:-6px;
      top:12px;
      position: absolute;
      content: "";
      display: inline-block;
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-right: 6px solid rgba(0,0,0,0.8);;
      border-bottom: 6px solid transparent;
    }
  }
}
</style>
