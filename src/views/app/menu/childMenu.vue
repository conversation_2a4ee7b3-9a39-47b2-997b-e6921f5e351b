<template>
    <div class="qiankun-menu-child" >
        <div class="child-menu-wrap" v-for="(menu,i) in data.children" :key="i">
            <div v-if="!menu.children || !menu.children.length" class="child-menu" @click="goMenuUrl(menu.href)" :class="{active:getActive(menu)}">
                <div class="text" :title="menu.name">{{ menu.name }}</div>
                <i class="hermes hermes-shoucang" :class="{active:getIsCollectActive(menu)}" @click.stop="handlerCollect(menu)"></i>
            </div>
            <div v-else class="child-menu-tree">
                <div class="title">{{ menu.name }}</div>
                <div class="child-menu-list">
                  <div class="child-menu" v-for="(childMenu,j) in getChildMenu(menu)" :key="j"  @click="goMenuUrl(childMenu.href)" :class="{active:getActive(childMenu)}">
                    <div class="text" :title="childMenu.name">{{ childMenu.name }}</div>
                    <i class="hermes hermes-shoucang" :class="{active:getIsCollectActive(childMenu)}" @click.stop="handlerCollect(childMenu)"></i>
                </div>
                </div>
            </div>
        </div>

    </div>
</template>
<script>
import { create } from 'lodash'

export default {
  props: {
    data: {}
  },
  data () {
    return {

    }
  },
  created () {
    // console.log(this.data)
  },
  computed: {
    hasThreeChild () {
      const list = this.data.children
      for (let i = 0; i < list.length; i++) {
        if (list[i].children && list[i].children.length) {
          return true
        }
      }
      return false
    },
    collectionList () {
      return this.$store.state.collectionList
    }
  },
  methods: {
    getIsCollectActive (menu) {
      return this.collectionList.find(it => it.menuId === menu.id)
    },
    async handlerCollect (menu) {
      await this.$store.dispatch('toggleCollection', menu.id)
    },
    getChildMenu (menu) {
      if (menu.children && menu.children.length) {
        return menu.children
      }
      return []
      // return [menu]
    },
    getActive (it) {
      if (it.href) {
        return it.href.replace(/^\//, '') === this.$route.hash.slice(1).replace(/^\//, '')
      }
      return false
    },
    goMenuUrl (href) {
      this.$emit('goMenuUrl', href)
    }
  }
}
</script>

<style lang="less">
.el-popper.qiankun-child-menu-popover{
    border-radius: 14px;
    padding: 0;
    .el-popover__title{
        display: flex;
        padding: 10px 8px;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        border-bottom: 1px solid #E6E8EB;
        color: #0D1B3F;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: 20px;
        margin-bottom: 0;
    }
    .qiankun-menu-child{
        padding: 4px;
        max-height: 600px;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        gap:8px 2px;
        writing-mode:vertical-lr;
        .child-menu-wrap{
          writing-mode:horizontal-tb;
          .child-menu-list{
            display: flex;
            flex-direction: column;
            gap:2px;
          }
        }
        .child-menu{
            display: flex;
            width: 180px;
            box-sizing: border-box;
            padding: 10px 16px 10px 16px;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            border-radius: 8px;
            font-size: 12px;
            line-height: 14px;
            &.active,&:hover.active{
                background: #DEDDFF;
            }
            &:hover{
                background: #F8F6FE;
                i{
                    display: initial;
                }
            }
            .text{
              max-width: 116px;
              line-height: 16px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            i{
                display: none;
                font-size: 14px;
                color: rgba(158, 164, 178, 1);
                &.active{
                  color: #5F3BCE;
                }
            }
        }
        .child-menu-tree{
          display: flex;
          flex-direction: column;
          gap:2px;
            .title{
                display: flex;
                padding: 8px 16px;
                color: #0D1B3F;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 600;
                line-height: 20px;
            }
            .child-menu{
              padding-left: 32px;
            }
        }
    }
}
</style>
