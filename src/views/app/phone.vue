<template>
    <div class="phone-page">
        <div class="block1">
            <img src="https://oss.syounggroup.com/bigfile/defaultTenantId/Frame 1321315523.png" alt="">
            <div class="col1">
                <div class="row1">HERMES</div>
                <div class="row2">品牌五指操作平台</div>
            </div>
        </div>
        <div class="block2">
            <img src="https://oss.syounggroup.com/bigfile/defaultTenantId/嗨%2C欢迎访问.svg" alt="">
        </div>
        <div class="block3">
            <img src="https://oss.syounggroup.com/bigfile/defaultTenantId/24120918185255162955737908 (1) 1.png" alt="">
        </div>
        <div class="block4">
            <img src="https://oss.syounggroup.com/bigfile/defaultTenantId/“让品牌经营更简单”.svg" alt="">
        </div>
        <div class="block5">
            <div class="box">
                <img src="https://oss.syounggroup.com/bigfile/defaultTenantId/全要素、 一站式、 数智协同.svg" alt="">
            </div>
        </div>
        <div class="page-bpttom">
            <div class="block6">
                目前仅支持“PC端浏览器”访问,快去试试吧!~
            </div>
            <div class="block7">
                https://hermes.syounggroup.com/
            </div>
            <div class="block8">
                <div class="btn" @click="copy">复制网址</div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
  methods: {
    copy () {
      this.copyToClipboard('https://hermes.syounggroup.com/')
      this.$message({
        customClass: 'inlineMessage',
        message: '复制成功！',
        type: 'success'
      })
    },
    copyToClipboard (text) {
      if (navigator.clipboard && window.isSecureContext) {
        // 使用 Clipboard API
        navigator.clipboard.writeText(text).then(() => {
          console.log('Text copied to clipboard')
        }).catch(err => {
          console.error('Failed to copy text: ', err)
        })
      } else {
        // 退回到使用 document.execCommand('copy') 方法
        const textArea = document.createElement('textarea')
        textArea.value = text
        // 避免在页面上显示
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        try {
          document.execCommand('copy')
          console.log('Text copied to clipboard')
        } catch (err) {
          console.error('Failed to copy text: ', err)
        }
        document.body.removeChild(textArea)
      }
    }

  }
}
</script>

<style lang="less">
.inlineMessage.el-message {
    min-width: unset;
}

.phone-page {
    font-family: "PingFang SC";
    width: 100vw;
    height: 100vh;
    background-image: url('https://oss.syounggroup.com/bigfile/defaultTenantId/20241210-182937.jpg');
    background-size: 100% 100%;
    box-sizing: border-box;
    overflow: auto;

    .block1 {
        margin-top: 7.33vw;
        margin-left: 7.33vw;
        display: flex;
        align-items: center;

        img {
            width: 7.8vw;
            height: 7.8vw;
            margin-right: 1.34vw;
        }

        .col1 {
            color: #5F3BCE;

            .row1 {
                font-weight: bold;
                font-size: 3.8vw;
            }

            .row2 {
                font-weight: bold;
                font-size: 2.0572vw;
            }
        }
    }

    .block2 {
        text-align: center;
        margin-top: 15.74vw;

        img {
            width: 31.87vw;
        }
    }

    .block3 {
        text-align: center;
        margin-top: 4.267vw;

        img {
            width: 66.67vw;
            height: 66.67vw;
        }
    }

    .block4 {
        text-align: center;
        margin-top: 5.34vw;

        img {
            width: 50vw;
        }
    }

    .block5 {
        text-align: center;
        margin-top: 3.47vw;

        .box {
            border-radius: 100vw;
            border: 1px solid #FFF;
            background: linear-gradient(90deg, #9DA7FF 0%, #AE94FF 100%);
            padding: 1.95vw 4.88vw;
            display: inline-flex;
            align-items: center;
        }

        img {
            width: 40.54vw;
        }
    }

    .block6 {
        text-align: center;
        color: #2B2B2C;
        font-size: 3.2vw;
        margin-top: 36vw;
    }

    .block7 {
        text-align: center;
        color: #3370FF;
        font-size: 3.2vw;
        margin-top: 1.2vw;
        text-decoration: underline;
    }

    .block8 {

        text-align: center;
        margin-top: 2vw;

        .btn {
            font-size: 2.67vw;
            display: inline-flex;
            align-items: center;
            color: #fff;
            background-color: #5F3BCE;
            border-radius: 100vw;
            padding: 1.4vw 4.88vw;
        }
    }

    .page-bpttom{
        position: absolute;
        width: 100vw;
        bottom: 8vw;
    }

}
</style>
