<template>
  <div class="qiankun-task-list">
    <i
      class="task-list-icon hermes hermes-oms_qd_ly_rwzx icon-qd_ly_rwzx"
      @click="drawer = true"
    />
    <el-drawer title="任务中心" :visible.sync="drawer" direction="rtl">
      <div class="task-list-wrap">
        <el-card v-for="task in taskList" :key="task.uid" class="task-list-item">
          <div slot="header" class="title">{{ task.name }}</div>
          <el-progress
            :text-inside="true"
            :stroke-width="16"
            :percentage="task.progress"
            :status="getTaskStatus(task)"
          ></el-progress>
        </el-card>
      </div>
    </el-drawer>
  </div>
</template>
<script>
export default {
  name: 'taskList',
  data () {
    return {
      drawer: false
    }
  },
  computed: {
    taskList () {
      return this.$store.state.taskList
    }
  },
  methods: {
    getTaskStatus (task) {
      if (task.progress <= 5) {
        return 'warning'
      }
      if (task.progress <= 95) {
        return 'format'
      }
      return 'success'
    }
  }
}
</script>
<style scoped lang="less">
.task-list-icon {
  line-height: 36px;
  font-size: 18px;
  margin-right: 6px;
  cursor: pointer;
}
.task-list-wrap {
  padding: 10px;
  .task-list-item {
    text-align: left;
    font-size: 14px;
    margin-bottom: 10px;
  }
}
</style>
<style lang="less">
.qiankun-task-list{
    .el-card__header{
        padding: 4px 10px;
    }
}
</style>
