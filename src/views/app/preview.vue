<template>
  <div class="qiankun-preview" v-if="showPreview">
    <el-image-viewer
      v-if="showType === 'image'"
      :urlList="image.previewImages"
      :on-close="closeViewer"
    ></el-image-viewer>
    <div v-if="showType === 'excel'" class="qiankun-preview-luckysheet">
      <div
        id="qiankun-preview-luckysheet"
        class="qiankun-preview-luckysheet"
      ></div>
      <button
        class="qiankun-preview-luckysheet-btn"
        @click="downloadLuckysheet"
      >
        下载文件<i class="el-icon-download el-icon--right"></i>
      </button>
    </div>
    <div class="qiankun-preview-word" v-if="showType === 'word'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div ref="word"></div>
    </div>
    <div class="qiankun-preview-video" v-if="showType === 'video'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div id="qiankun-preview-video"></div>
    </div>
    <div class="qiankun-preview-audio" v-if="showType === 'audio'">
      <i class="el-icon-close" @click="closeViewer"></i>
      <div class="qiankun-preview-audio-wrap">
        <div class="canvas" ref="qiankun-preview-audio"></div>
        <div class="control">
          <i class="audio-play el-icon-arrow-left" @click="skipBackward"></i>
          <i
            class="audio-play"
            @click="audioPlay"
            :class="`${
              audio.isplay ? 'el-icon-video-pause' : 'el-icon-video-play'
            }`"
          ></i>
          <i class="audio-play el-icon-arrow-right" @click="skipForward"></i>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

import axios from 'axios'

const docx = require('docx-preview')
const FILE_TYPE = {
  pdf: ['pdf'],
  image: ['png', 'jpg', 'jpeg', 'gif'],
  excel: ['xlsx'],
  word: ['docx'],
  video: ['mp4', 'webm', 'ogv', 'm3u8', 'flv'],
  audio: ['ogg', 'mp3']
}

const EXCEL_CONFIG = {
  container: 'qiankun-preview-luckysheet',
  lang: 'zh',
  myFolderUrl: '',
  allowCopy: true, // 是否允许拷贝
  showtoolbar: false, // 是否显示工具栏
  showinfobar: true, // 是否显示顶部信息栏
  showsheetbar: true, // 是否显示底部sheet页按钮
  showstatisticBar: true, // 是否显示底部计数栏
  sheetBottomConfig: true, // sheet页下方的添加行按钮和回到顶部按钮配置
  allowEdit: false, // 是否允许前台编辑
  enableAddRow: false, // 允许增加行
  enableAddCol: false, // 允许增加列
  userInfo: false, // 右上角的用户信息展示样式
  showRowBar: true, // 是否显示行号区域
  showColumnBar: true, // 是否显示列号区域
  sheetFormulaBar: false, // 是否显示公式栏
  enableAddBackTop: false, // 返回头部按钮
  rowHeaderWidth: 0, // 纵坐标
  columnHeaderHeight: 0, // 横坐标
  showstatisticBarConfig: {
    count: true,
    view: true,
    zoom: true
  },
  showsheetbarConfig: {
    add: false, // 新增sheet
    menu: true, // sheet管理菜单
    sheet: true // sheet页显示
  }
}

export default {
  data () {
    return {
      showPreview: false,
      showType: null,
      image: {
        previewImages: []
      },
      video: {
        url: ''
      },
      excel: {
        url: ''
      },
      audio: {
        isplay: false,
        url: ''
      }
    }
  },
  components: {
    ElImageViewer
  },
  methods: {
    // 下载excel
    downloadLuckysheet () {
      window.open(this.excel.url)
    },
    // 获取文件类型
    getType (data) {
      // 文件地址
      if (typeof data === 'string') {
        const suffix = this.getUrlSuffix(data)
        for (const key in FILE_TYPE) {
          if (FILE_TYPE[key].indexOf(suffix) !== -1) {
            return key
          }
        }
      }
    },
    // 获取文件后缀名
    getUrlSuffix (url) {
      const arr = url.split('.')
      return arr[arr.length - 1].toLowerCase()
    },
    $preview (data, type) {
      if (!type) {
        type = this.getType(data)
      }
      if (type === 'excel') {
        window.open(`${window.QIANKUN_DATA.origin}/preview?src=${encodeURI(data)}`)
      } else {
        this.preview(data, type)
      }
    },
    preview (data, type) {
      this.showPreview = true
      if (!type) {
        type = this.getType(data)
      }
      this.showType = type
      // 支持服务端接口文件预览
      if (typeof data === 'object' && !Array.isArray(data)) {
        data = window.URL.createObjectURL(data)
      }
      setTimeout(() => {
        if (type === 'image') {
          if (!Array.isArray(data)) {
            data = [data]
          }
          this.image.previewImages = data
          return false
        }
        if (type === 'excel') {
          this.previewExcel(data)
          return false
        }
        if (type === 'word') {
          this.previewWord(data)
          return false
        }
        if (type === 'video') {
          this.previewVideo(data)
          return false
        }
        if (type === 'audio') {
          this.previewAudio(data)
          return false
        }
        window.open(data)
      })
    },
    downFile (href) {
      var eleLink = document.createElement('a')
      eleLink.download = this.$utils.getFileName(href)
      eleLink.style.display = 'none'
      eleLink.href = href
      // 触发点击
      document.body.appendChild(eleLink)
      eleLink.click()
      // 然后移除
      document.body.removeChild(eleLink)
    },
    closeViewer () {
      this.showPreview = false
      if (this.wavesurfer) {
        this.wavesurfer.destroy()
        this.wavesurfer = null
      }
    },
    // 预览Word
    previewWord (url) {
      axios({
        method: 'get',
        responseType: 'blob', // 设置响应文件格式
        url
      }).then(({ data }) => {
        docx.renderAsync(data, this.$refs.word) // 渲染到页面预览
      })
    },
    // 音频后退
    skipBackward () {
      this.wavesurfer && this.wavesurfer.skipBackward()
    },
    // 音频前进
    skipForward () {
      this.wavesurfer && this.wavesurfer.skipForward()
    },
    // 预览音频
    previewAudio (url) {
      this.audio.url = url
      setTimeout(async () => {
        const WaveSurfer = await import('wavesurfer.js').then(res => res.default)
        this.wavesurfer = WaveSurfer.create({
          container: this.$refs['qiankun-preview-audio'],
          barWidth: 2,
          waveColor: '#5F3BCE',
          progressColor: '#ebebeb',
          barHeight: 1, // the height of the wave
          barGap: null // the optional spacing between bars of the wave, if not provided will be calculated in legacy format
        })
        this.wavesurfer.load(url)
      }, 0)
    },
    // 播放音频
    audioPlay () {
      if (this.wavesurfer) {
        this.wavesurfer.playPause()
        this.audio.isplay = this.wavesurfer.isPlaying()
      }
    },
    // 预览视频
    async previewVideo (url) {
      const Player = await import('xgplayer').then(res => res.default)
      const Mp4Player = await import('xgplayer-mp4').then(res => res.default)
      const HlsJsPlayer = await import('xgplayer-hls.js').then(res => res.default)
      const FlvJsPlayer = await import('xgplayer-flv.js').then(res => res.default)
      this.video.url = url
      let Video = Player
      const suffix = this.getUrlSuffix(url)
      if (suffix === 'mp4') {
        Video = Mp4Player
      } else if (suffix === 'm3u8') {
        Video = HlsJsPlayer
      } else if (suffix === 'flv') {
        Video = FlvJsPlayer
      }
      new Video({
        id: 'qiankun-preview-video',
        url
      })
    },
    // 预览Excel
    previewExcel (url) {
      this.excel.url = url
      const name = this.getFileName(url)
      setTimeout(async () => {
        const LuckyExcel = await import('luckyexcel').then(res => res.default)
        LuckyExcel.transformExcelToLuckyByUrl(
          url,
          name,
          (exportJson, luckysheetfile) => {
            window.luckysheet.create({
              data: exportJson.sheets,
              title: exportJson.info.name,
              userInfo: exportJson.info.name.creator,
              hook: {},
              ...EXCEL_CONFIG
            })
            setTimeout(() => {
              document.getElementById('luckysheet_info_detail_title').onclick =
                (event) => {
                  window.luckysheet.destroy()
                  this.closeViewer()
                }
            }, 0)
          }
        )
      }, 0)
    }
  }
}
</script>
<style lang="less">
.qiankun-preview {
  .el-icon-close {
    position: fixed;
    top: 40px;
    right: 40px;
    width: 40px;
    height: 40px;
    font-size: 24px;
    color: #fff;
    background-color: #606266;
    border-radius: 100px;
    text-align: center;
    line-height: 40px;
    cursor: pointer;
  }
}
.qiankun-preview-audio {
  background-color: gray;
  z-index: 20000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  .qiankun-preview-audio-wrap {
    position: relative;
    background-color: #fff;
    width: 1056px;
    height: 200px;
    padding: 16px;
    background: #000;
  }
  .canvas {
    // position: absolute;
    // top:100px;
    width: 1024px;
    height: 140px;
  }
  .control {
    text-align: center;
    margin-top: 10px;
    .audio-play {
      font-size: 28px;
      color: #fff;
      cursor: pointer;
    }
  }
}
.qiankun-preview-video {
  background-color: gray;
  z-index: 20000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
}
.qiankun-preview-word {
  background-color: gray;
  z-index: 20000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: auto;
}
.qiankun-preview-luckysheet {
  z-index: 20000;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  .luckysheet_info_detail_back{
    display: none;
  }
  .qiankun-preview-luckysheet-btn {
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 400;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    cursor: pointer;
    background: #fff;
    border: 1px solid #e6e8eb;
    color: #fff;
    background-color: #5f3bce;
    border-color: #5f3bce;
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    position: fixed;
    right: 20px;
    top: 12px;
    z-index: 20000;
    &:hover {
      background: #7f62d8;
      border-color: #7f62d8;
      color: #fff;
    }
  }
  #luckysheet_info_detail_input {
    pointer-events: none;
  }
  .luckysheet-share-logo,
  .luckysheet_info_detail_update,
  .luckysheet_info_detail_save {
    display: none;
  }
}
#luckysheet-tooltip-up {
  display: none;
}
</style>
