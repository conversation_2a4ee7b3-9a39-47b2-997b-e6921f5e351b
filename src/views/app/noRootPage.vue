<template>
    <div class="qiankun-overview-oa" v-if="overview.show" :class="{collapse}">
      <img :src="overview.img" alt="">
      <div class="btn-wrap">
        <el-button type="primary" @click="goOa">立即申请权限</el-button>
        <el-button @click="closeOverview" v-if="overview.hasBkBtn">返回</el-button>
      </div>
    </div>
</template>

<script>
export default {
  props: {
    goFeishuChart: {},
    adminMap: {},
    collapse: {}
  },
  data () {
    return {
      overview: {
        hasBkBtn: true, // 是否有返回按钮
        show: false,
        img: ''
      }
    }
  },
  watch: {
    // 监听路有变化切换系统
    async $route (v, o) {
      if (v.path !== o.path) {
        this.closeOverview()
      }
    }
  },
  methods: {
    goOa () {
      window.open('https://oss.syounggroup.com/static/file/syounger-work-app/login/get-oa-url.html?target_uri=https%3A%2F%2Foasyi.syounggroup.com%2Fweb%2F%23%2Fcurrent%2Fkm-review%2FkmReviewMain%2Fadd%2F1hs4spd2fw5w8qogw1rlve8v1i9ri061eew1')
    },
    closeOverview () {
      this.overview.show = false
      this.scopeActive = ''
    },
    // 打开无权限弹窗
    openNoRoot (key, hasBkBtn, url) {
      const names = this.adminMap[key]
      const imgMap = {
        'commodity-center': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/commodity-center-new.png',
        'amb-service': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/commodity-center.png',
        'athena-service': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/kol.png',
        'oms-main-app': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/oms.png',
        'soyoung-zg': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/soyoung-zg.png',
        'syoung-store': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/syoung-store.png',
        'brand-member-service': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/brand-member-service.png',
        'hera-service': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/hera-service.png',
        finance: 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/financial.png',
        'psv-tower': 'https://oss.syounggroup.com/static/file/defaultTenantId/staging/psv-tower.png'
      }
      if (imgMap[key]) {
        setTimeout(() => { // 延迟打开，防止路由变化直接关闭
          this.$store.commit('setUserInfo', {
            ...window.QIANKUN_DATA.user_info,
            menus: []
          })
          if (url) {
            this.$router.push(url)
          }
          this.openNoRootOa(names, key, imgMap[key], hasBkBtn)
        }, 0)
      } else {
        this.openNoRootMsg(names)
      }
    },

    // 暂无系统权限，打开弹窗
    openNoRootOa (names, key, url, hasBkBtn) {
      this.scopeActive = key
      this.overview.show = true
      this.overview.img = url
      this.overview.hasBkBtn = false
      // this.overview.hasBkBtn = hasBkBtn
    },

    // 暂无权限，简单提示
    openNoRootMsg (names) {
      const that = this
      const h = this.$createElement
      this.$message({
        // duration: 0,
        message: h('span', { class: 'el-message__content lw-no-root-oa' }, [
          h('span', '暂无权限，需走OA流程申请权限。如有疑问点击咨询 '),
          ...names.split('、').map(name => {
            return h('a', {
              on: {
                click () {
                  that.goFeishuChart(name)
                }
              }
            }, [
              h('img', { attrs: { src: 'https://oss.syounggroup.com/static/file/defaultTenantId/飞书图标.png' } }, `${name}`),
                `${name}`])
          }),
          h('span', '。')
        ]),
        type: 'warning'
      })
    }
  }
}
</script>

<style lang="less">
.qiankun-overview-oa{
  position: fixed;
  top:49px;
  left: 120px;
  right:0;
  bottom: 0;
  z-index: 3000;
  overflow: auto;
  overflow-x: hidden;
  background-color: #fff;
  box-sizing: border-box;
  padding-bottom: 42px;
  &.collapse{
    left: 80px;
    .btn-wrap{
      left: 80px;
    }
  }
  img{
    box-sizing: border-box;
    width: 100%;
  }
  .btn-wrap{
    border-top: 1px solid #ebeef5;
    padding: 4px 0;
    position: fixed;
    bottom: 0;
    right: 0;
    left: 120px;
    background-color: #fff;
    text-align: center;
  }
}
</style>
