<template>
  <div class="qiankun-page-nav">
    <!-- 控制菜单模块 -->
    <div class="page-home">
      <el-tooltip class="item" effect="dark" :content="collapse ? '展开菜单' : '收起菜单'" placement="bottom">
        <div class="fold-btn" @click="$emit('update:collapse', !collapse)">
          <i class="pointer hermes" :class="collapse ? 'hermes-caidanzhankai' : 'hermes-caidanshouqi'" />
        </div>
      </el-tooltip>
      <div class="home-btn" @click="goHome" v-show="showOverview">概况</div>
    </div>
    <!-- 标签页模块 -->
    <el-tabs class="page-tabs" :value="activePageNav && activePageNav.qiankunKey" type="card"
      :closable="tabList.length > 1" @tab-remove="removeTab" @tab-click="tabClick"
      @contextmenu.prevent.native="openMenu($event)">
      <el-tab-pane v-for="item in tabList" :key="item.qiankunKey" :name="item.qiankunKey">
        <span slot="label" @dblclick="openNewWind(item)">
          <!-- <i :class="`${hasCollection(item) ? 'el-icon-star-on' : 'el-icon-star-off'} ${canSaveContextmenuPage(item) ? '' : 'disabled'}`"
            @click.stop="setCollapse(!hasCollection(item), item)"></i> -->
          <span>{{ getTabLanel(item) }}</span>
        </span>
      </el-tab-pane>
    </el-tabs>
    <!-- 下拉更多菜单 -->
     <div class="more-page-nav">
      <el-dropdown trigger="hover">
        <span class="el-dropdown-link">
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown" class="qiankun-more-page-nav-wrap">
          <el-dropdown-item>
            <div class="title" @click.stop>打开的标签页</div>
            <div class="menu-sys-list-wrap" @click.stop>
              <div class="menu-sys-item" v-for="(it,i) in moreTabs" :key="i">
                  <div class="menu-sys">{{ getAppnameByTenantId(it.tenantId) }}</div>
                  <div class="menu-wrap-list">
                    <div class="mene-item" :class="{active:activePageNav && activePageNav.qiankunKey === jt.qiankunKey}" v-for="(jt,j) in it.children" :key="j" @click="tabClick(jt)">
                      <div class="text">{{ jt.name }}</div>
                      <i class="el-icon-error" @click.stop="closePageNav('Self',jt.index)"></i>
                    </div>
                  </div>
              </div>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <!-- 收藏列表模块 -->

     </div>
    <!-- 右键菜单模块 -->
    <ul class="contextmenu" v-if="contextmenu.visible"
      :style="{ left: `${contextmenu.left}px`, top: `${contextmenu.top}px` }">
      <li @click="setCollapse(true)" v-if="!isCollectionContextmenu && canSaveContextmenu">
        <i class="hermes hermes-wodeshoucangjia"></i>
        <span>添加收藏</span>
      </li>
      <li @click="setCollapse(false)" v-if="isCollectionContextmenu && canSaveContextmenu">
        <i class="hermes hermes-wodeshoucangjia"></i>
        <span>取消收藏</span>
      </li>
      <!-- <li v-show="tabList.length > 1" @click="closePageNav('Self')">
        <i class="hermes hermes-wodeshoucangjia"></i>
        <span>关闭窗口</span>
      </li> -->
      <li @click="closePageNav('Left')">
        <i class="hermes hermes-guanbizuoce"></i>
        <span>关闭左侧</span>
      </li>
      <li @click="closePageNav('Right')">
        <i class="hermes hermes-guanbiyouce"></i>
        <span>关闭右侧</span>
      </li>
      <li @click="closePageNav('Other')">
        <i class="hermes hermes-a-guanbiqita31"></i>
        <span>关闭其他</span>
      </li>
    </ul>

  </div>
</template>
<script>
import utils from '@/utils'
import Sortable from 'sortablejs'
// import taskList from './taskList.vue'
export default {
  name: 'qiankun-page-nav',
  props: {
    collapse: {
      // 菜单是否展开
      type: Boolean,
      default: false
    }
  },
  components: {
    // taskList
  },
  data () {
    return {
      contextmenu: {
        // 右键菜单数据
        index: 0,
        visible: false,
        top: 0,
        left: 0
      },
      showCollectTenantId: []
    }
  },
  computed: {
    moreTabs () {
      const ret = []
      this.tabList.forEach((it, index) => {
        const item = ret.find(jt => it.tenantId === jt.tenantId)
        if (!item) {
          ret.push({
            ...it,
            children: [{ ...it, index }]
          })
        } else {
          item.children.push({ ...it, index })
        }
      })
      return ret
    },
    // 不显示概览的租户id
    showOverview () {
      const tenantIds = ['hermes-workspace']
      return !tenantIds.includes(this.activePageNav && this.activePageNav.tenantId)
    },
    // 根据路由获取到的选中项
    activePageNav () {
      return this.$store.state.activePageNav
    },
    // 标签页列表
    tabList () {
      return this.$store.state.pageNav
    },
    // 收藏start
    collectionList () {
      return this.$store.state.collectionList
    },
    // 右键选中的菜单项数据
    contextmenuData () {
      return this.tabList[this.contextmenu.index]
    },
    // 是否已经收藏当前选中标签页
    isCollectionContextmenu () {
      if (!this.contextmenuData || !this.collectionList) {
        return false
      }
      return !!this.collectionList.find(it => it.qiankunKey === this.contextmenuData.qiankunKey)
    },
    // 是否可收藏属性：非菜单级页面、不显示页面不可收藏
    canSaveContextmenu () {
      return (
        this.contextmenuData?.type === 'router' &&
        this.contextmenuData.isShow !== '0'
      )
    }
  },
  async created () {
    // 监听全局点击事件，隐藏右键菜单
    document.body.addEventListener('click', this.setContextmenuHide)
  },
  mounted () {
    // 设置pageNav拖拽排序
    // this.setSortPageNav()
  },
  beforeDestroy () {
    // 摧毁全局点击事件
    document.body.removeEventListener('click', this.setContextmenuHide)
  },
  methods: {
    // 根据租户id获取应用名称
    getAppnameByTenantId: utils.getAppnameByTenantId,
    // 跳转到系统首页
    goHome () {
      this.$router.push({ path: `/${window.QIANKUN_DATA.localPathname}` })
    },
    /** SRART:标签页函数块 **/
    canSaveContextmenuPage (item) {
      if (!item) {
        return false
      }
      if (!item.type) {
        Object.assign(item, { type: 'router', isShow: '1' })
      }
      return item?.type === 'router' &&
        item.isShow !== '0'
    },
    // 页签移除
    removeTab (name, cb) {
      this.closePageNav(
        'Self',
        this.tabList.findIndex((it) => it.qiankunKey === name)
      )
    },
    // 页签点击,存在切换系统
    tabClick (v) {
      const item = this.tabList.find((it) => it.qiankunKey === (v.qiankunKey || v.name))
      if (item) {
        utils.goSystemUrl.call(this, item.pathname, item.fullPath)
      }
    },
    // 关闭标签页
    closePageNav (name, index) {
      if (this.tabList.length <= 1) {
        this.$message({
          message: '无法关闭，至少保留一个标签页',
          type: 'warning'
        })
        return
      }
      this.$store.commit(
        `close${name}PageNav`, index === undefined ? this.contextmenu.index : index
      )
      utils.afterCloseTabHandleRoute(this.$route, this.$router)
    },
    // 获取tab标题,通过路由meta显示或者mushroom配置
    getTabLanel (tab) {
      return tab?.meta?.title || tab.name
    },
    // 打开新页面
    openNewWind (nav) {
      // const { origin, pathname } = window.location
      // window.open(`${origin}${pathname}#${nav.fullPath}`)
    },
    // 全局点击事件回调;设置隐藏右键菜单
    setContextmenuHide () {
      this.contextmenu.visible = false
    },
    // 打开右键菜单,设置菜单数据
    openMenu (e) {
      const el = this.getElUtilsTarget(e.target, 'el-tabs__item', 'el-tabs')
      if (el) {
        const index = Array.from(el.parentNode.children).findIndex(
          (it) => it === el
        )
        this.contextmenu.visible = true
        this.contextmenu.left = e.pageX
        this.contextmenu.top = e.pageY
        this.contextmenu.index = index
      } else {
        this.contextmenu.visible = false
      }
    },
    /** END: 标签页函数块 **/

    /** SRART:收藏功能函数块 **/
    // 判断是否收藏
    hasCollection (pageNavItem) {
      return this.collectionList.find(
        (it) => it.qiankunKey === pageNavItem.qiankunKey
      )
    },
    // 设置是否常见
    async setCollapse (isSave, item) {
      item = this.contextmenuData
      if (item && !this.canSaveContextmenuPage(item)) {
        return false
      }
      await this.$store.dispatch('toggleCollection', item.menuId)
    },
    /** END:收藏功能函数块 **/

    /** START:工具函数 **/
    // 循环遍历找到tabsItem节点;
    getElUtilsTarget (el, targetClass, EndClass) {
      while (
        !el.classList.contains(targetClass) &&
        !el.classList.contains(EndClass)
      ) {
        el = el.parentNode
      }
      if (el.classList.contains(targetClass)) {
        return el
      }
    }
    /** END:工具函数 **/
  }
}
</script>

<style lang="less">
.qiankun-more-page-nav-wrap.el-dropdown-menu{
  box-sizing: border-box;
  padding-top: 0;
  border-radius: 14px;
  overflow: hidden;
  .el-dropdown-menu__item{
    background-color:#ffffff !important;
    color: #0D1B3F !important;
    padding: 0 !important;
    .title{
      display: flex;
      padding: 10px 8px;
      align-items: center;
      gap: 10px;
      align-self: stretch;
      border-bottom: 1px solid #E6E8EB;
      color: #0D1B3F;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 600;
      line-height: 20px;
    }
    .menu-sys-list-wrap{
      padding: 0 4px;
      max-height: 600px;
      overflow-y: auto;
      overflow-x: hidden;
      .menu-sys-item{
        width: 180px;
      }
      .menu-sys{
        display: flex;
        width: 180px;
        padding: 10px 16px;
        align-items: center;
        gap: 10px;
        font-weight: bold;
      }
      .menu-wrap-list{
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
      .mene-item{
        display: flex;
        width: 180px;
        padding: 10px 16px 10px 32px;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        box-sizing: border-box;
        font-size: 12px;
        &:hover{
          border-radius: 8px;
          background: #F8F6FE;
          i{
            display: initial;
          }
        }
        &.active{
          border-radius: 8px;
          background: #E2E1FF;
          i{
            display: initial;
          }
        }
        .text{
          line-height: 16px;
          max-width: 106px;
        }
        i{
          font-size: 16px;
          color: #5F3BCE;
          display: none;
        }
      }
    }
  }
}

.page-nav-draging {
  opacity: 0 !important;
}

.qiankun-page-nav {
  position: relative;
  display: flex;
  flex-flow: row;
  height: 36px;
  padding: 0 0px 0 0;
  text-align: center;
  color: #72777a;
  background-color: #ffffff;

  .more-page-nav .el-dropdown{
    height: 100%;
    width: 34px;
    // box-shadow: -4px 0px 10px 0px rgba(192, 190, 190, 0.22);
    .el-dropdown-link{
      cursor: pointer;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    i{
      font-size: 16px;
      color: #555F78;
    }
  }

  .contextmenu {
    position: fixed;
    background: #ffffff;
    margin: 0;
    background: #fff;
    z-index: 3000;
    list-style-type: none;
    padding: 6px;
    border-radius: 14px;
    border: 1px solid #E4E7ED;
    box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.10);
    font-size: 12px;
    font-weight: 400;
    color: #333;
    display: flex;
    flex-direction: column;
    gap:2px;

    li {
      margin: 0;
      padding: 10px 20px;
      cursor: pointer;
      border-radius: 8px;
      display: flex;
      align-items: center;

      i{
        margin-right: 10px;
        font-size: 16px;
      }

      &:hover {
        background-color: #F8F6FE;
      }
    }
  }

  .page-home {
    display: flex;
    flex-flow: row;
    align-items: center;
    text-align: center;
    justify-content: center;

    .pointer {
      font-size: 17px;
    }

    .fold-btn {
      height: 25px;
      width: 25px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 8px;
      padding: 0;
      border-radius: 2px;
      color: rgba(62, 73, 101, 1);

      &:hover {
        // background: rgba(0, 0, 0, 0.025);
      }
    }

    .home-btn {
      cursor: pointer;
      // flex: 1;
      margin-left: 18px;
      font-size: 14px;
      position: relative;
      display: inline-flex;
      align-items: center;

      &::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 12px;
        background-color: #CFD1D9;
        margin-left: 22px;
      }
    }
  }

  .page-tabs.el-tabs--top.el-tabs--card {
    padding: 0 10px;
    flex:1;
    overflow: hidden;
    user-select: none;

    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 36px;
      display: none;
    }

    .el-tabs__header {
      margin: 0;
      border: 0;

      .el-tabs__nav-wrap {
        padding: 0;
        &::after {
          height: 0;
          display: none;
        }

        .el-tabs__nav {
          border: 0;

          .el-tabs__item {
            display: inline-flex;
            height: 28px;
            padding: 0px 10px;
            margin: 4px 0px;
            justify-content: center;
            align-items: center;
            border: 0;
            border-radius: 100px;
            margin-left: 4px;

            i.disabled {
              color: rgba(110, 118, 140, 0.3);
              cursor: not-allowed;
            }

            .el-icon-star-on {
              color: #6E768C;
              font-size: 12px;
              margin-right: 4px;
            }

            .el-icon-star-off {
              color: #6E768C;
              font-size: 12px;
              margin-right: 4px;
            }
            &:hover{
              background-color: #F3F5F8;
            }

            &.is-active {
              color: #5F3BCE;
              background-color: #F3F5F8;

              .el-icon-close {
                width: 16px;
              }
            }

            &:hover {
              .el-icon-close {
                width: 16px;
              }
            }

            .el-icon-close {
              color: #7f62d8;
              font-size: 16px;
              line-height:16px;
              height: 16px;
              background-color: transparent;
              border: none;
              top:0px;
              &:hover{
              }
              &::before{
                transform:scale(1);
                content: "\e79d";
              }
            }
          }
        }
      }
    }
  }

  .collection-list-btn {
    height: 28px;
    display: flex;
    align-items: center;
    margin-top: 4px;
  }

  .demand-btn {
    margin-top: 4px;
    margin-left: 12px;
    height: 28px;
    display: flex;
    align-items: center;

    .hermes-jushou {
      font-size: 16px;
      position: relative;
      top: 1px;
    }
  }
}

.qiankun-collection-list-wrap {
  left: unset !important;
  right: 10px;
  width: 146px;

  .qiankun-page-nav-item.el-dropdown-menu__item {
    align-items: baseline;
    user-select: none;
    text-align: left;
    padding-right: 2px;

    font-size: 12px;

    &.is-disabled {
      font-size: 12px;
    }

    .collectionHandlerSort {
      margin-left: -4px;
    }

    .child-block>span {
      display: inline-block;
      width: 100px;
      margin-left: 8px;
    }

    .parent-block>span {
      display: inline-block;
      width: 98px;
    }

    .el-icon-close {}
  }

  .qiankun-page-nav-parent.el-dropdown-menu__item {
    padding-left: 8px;
    background-color: unset !important;
    color: unset !important;

    &.el-dropdown-menu__item--divided:before {
      margin: 0;
    }
  }
}
</style>
