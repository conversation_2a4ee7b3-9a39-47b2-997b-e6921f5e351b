import Vue from 'vue'
import VueRouter from 'vue-router'
import utils from '@/utils'
Vue.use(VueRouter)

const routes = []

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const includPush = VueRouter.prototype.push
VueRouter.prototype.push = function push (location) {
  return includPush.call(this, location).catch(err => err)
}

const router = new VueRouter({
  mode: 'history',
  base: '',
  routes
})

const qiankunV2AppPath = process.env.VUE_APP_MODE === 'production' ? [
  // '/oms',
  // '/soyoung-zg',
  // '/mars',
  // '/soyoung',
  '/newretail',
  '/syoung-store-admin',
  '/brand-member'
  // '/hera',
  // '/psv',
  // '/supplychain-stock',
  // '/finance',
  // '/finance-report'
]
  : [
    // '/brand',
  // '/commodity',
  // '/oms',
  // '/soyoung-zg',
  // '/mars',
  // '/soyoung',
    '/newretail',
    '/syoung-store-admin',
    // '/member-platform',
    '/brand-member'
  // '/hera',
  // '/psv',
  // '/supplychain-stock',
  // '/finance',
  // '/finance-report'
  ]

router.afterEach((vm) => {
  const that = Vue.prototype
  if (qiankunV2AppPath.includes(location.pathname)) {
    location.reload()
    return false
  }
})

// 点击埋点时间处理
// document.body.addEventListener('click', (e) => {
//   const that = Vue.prototype
//   utilsMenuShow(() => {
//     let el = e.target
//     for (let i = 0; i < 2; i++) {
//       if (!el) {
//         return false
//       }
//       const basicInfo = {
//         name: window.QIANKUN_STORE?.state?.activePageNav?.name,
//         menu_id: window.QIANKUN_STORE?.state?.activePageNav?.id,
//         report_app_name: 'hermes',
//         report_app_tenant: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
//         report_app_ext_tenant: getExtTenant(),
//         loginName: window.USER_INFO.user.loginName,
//         userName: window.USER_INFO.user.name
//       }
//       if (el.classList.contains('el-button')) {
//         that.$upEvent('Click', {
//           ...basicInfo,
//           event_name: el.innerText,
//           event_type: 'button'
//         })
//       }
//       if (el.classList.contains('el-tabs__item')) {
//         that.$upEvent('Click', {
//           ...basicInfo,
//           event_name: el.innerText,
//           event_type: 'tab'
//         })
//       }
//       el = el.parentElement
//     }
//   })
// }, true)

// function utilsMenuShow (fn) {
//   utils.untilRunFn(
//     () => {
//       return window.QIANKUN_STORE?.state?.activePageNav?.id
//     },
//     fn
//   )
// }
// 获取二级租户
function getExtTenant () {
  if (window.qiankunAppInfo && window.qiankunAppInfo.ext_tenant) {
    return window.qiankunAppInfo.ext_tenant
  }
  return window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id']
}

export default router
