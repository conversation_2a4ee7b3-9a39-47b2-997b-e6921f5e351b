<template>
    <div>
        <preview ref="preview"/>
    </div>
</template>
<script>
import preview from './views/app/preview.vue'
export default {
  components: {
    preview
  },
  data () {
    return {

    }
  },
  methods: {
    getParams () {
      const src = decodeURIComponent(location.search).slice(1).split('&')
      const ret = {}
      src.forEach(it => {
        const p = it.split('=')
        ret[p[0]] = p[1]
      })
      return ret
    },
    preview () {
      const params = this.getParams()
      this.$refs.preview.preview(params.src)
    }
  },
  mounted () {
    this.preview()
  }
}
</script>

<style scoped>

</style>
