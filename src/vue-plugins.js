import Vue from 'vue'
import api from '@/api'
import lowcodelib from 'lowcodelib'
import ElementUI from 'element-ui'
// import './style/index.scss'
import 'lowcodelib/theme/css/index.css'
import '@/icons'
// 收集菜单api映射表
import '@/menuApi.js'

const packageInfo = require('../package.json')
window.packageInfo = {
  version: packageInfo.version
}

// 注册全局组件
Vue.use(lowcodelib)
Vue.use(ElementUI)
Vue.use(api)

// 设置elementUI大小
Vue.prototype.$ELEMENT = { size: 'small', zIndex: 3000 }
Vue.prototype.SETTING = window.QIANKUN_SETTING
// 设置环境值
Vue.prototype.NODE_ENV = process.env.VUE_APP_MODE
Vue.prototype.IS_GRAY = process.env.VUE_APP_IS_GRAY
// 全局点击防抖处理
const on = Vue.prototype.$on
Vue.prototype.$on = function (event, func) {
  let timer
  let newFunc = func
  if (event === 'click') {
    newFunc = function () {
      if (timer) {
        clearTimeout(timer)
      }
      if (!timer) {
        func.apply(this, arguments)
      }
      timer = setTimeout(() => {
        timer = null
      }, 350)
    }
  }
  on.call(this, event, newFunc)
}

const baseUrl = process.env.VUE_APP_MODE === 'production' ? 'https://hermes.syounggroup.com/login' : 'https://testhermes.syounggroup.com/login'
// 退出登录
const logoutCallList = []
// 登录
window.QIANKUN_DATA.$goLogin = Vue.prototype.$goLogin = function () {
  let fail
  if (!location.pathname || location.pathname === '/') {
    fail = encodeURIComponent(`${baseUrl}`)
  } else {
    fail = encodeURIComponent(`${baseUrl}?backUrl=${encodeURIComponent(location.href)}&appid=${window.QIANKUN_DATA.active}`)
  }
  location.href = `${window.QIANKUN_SETTING.BASE_URL}/user/ssoRedirect?success=${encodeURIComponent(location.href)}&fail=${fail}&tenantId=HERMES&extTenantId=${window.QIANKUN_SETTING.EXT_TENANT_ID}&appId=${window.QIANKUN_SETTING.TENANT_ID}`
}
// 登出
window.QIANKUN_DATA.$goLogout = Vue.prototype.$goLogout = async function (backUrl) {
  // 清除标签session
  const userId = window.QIANKUN_DATA.user_info?.user.id
  const localPathname = location.pathname.replace(/\//g, '')
  userId && sessionStorage.clear(`qiankun_pageNav_${localPathname}_${userId}`)
  // 登录逻辑
  for (let i = 0; i < logoutCallList.length; i++) {
    await logoutCallList[i]()
  }
  backUrl = backUrl || window.QIANKUN_DATA.goLogoutBackUrl || location.href
  const redirect = encodeURIComponent(`${baseUrl}?backUrl=${encodeURIComponent(backUrl)}&appid=${window.QIANKUN_DATA.active}`)
  location.href = `https://passport.syounggroup.com/logout?service=${encodeURIComponent(`${window.QIANKUN_SETTING.BASE_URL}/user/logout?tenantId=${window.QIANKUN_SETTING.TENANT_ID}&extTenantId=${window.QIANKUN_SETTING.EXT_TENANT_ID}&logoutRequest=1&redirect=${redirect}`)}`
}

window.QIANKUN_DATA.$addListenerlogout = function (fn) {
  const index = logoutCallList.indexOf(fn)
  if (index === -1) {
    logoutCallList.push(fn)
  }
}

window.QIANKUN_DATA.$desListenerlogout = function (fn) {
  const index = logoutCallList.indexOf(fn)
  if (index !== -1) {
    logoutCallList.splice(index, 1)
  }
}

// 统一埋点
function ReportInstall (Vue) {
  const PAGE_ENTER_TIME = Date.now()
  const upTime = 3000 // 上报间隔时间
  const appVersion = '1.0.0' // 读取版本号
  const api = Vue.prototype.$api.batchReport // 批量埋点上传接口
  const report = new Vue.prototype.$utils.Report(appVersion, api, upTime, PAGE_ENTER_TIME)
  Vue.prototype.$upEvent = function (eventName, attrs) {
    report.$upEvent(eventName, attrs)
  }
  window.QIANKUN_DATA.$userUpEvent = function (eventName, attrs) {
    const basicInfo = {
      name: window.QIANKUN_STORE?.state?.activePageNav?.name,
      menu_id: window.QIANKUN_STORE?.state?.activePageNav?.id,
      report_app_name: 'hermes',
      report_app_tenant: window.QIANKUN_DATA.apiHeader['X-Tenant-Id'],
      report_app_ext_tenant: window.QIANKUN_DATA.apiHeader['X-Ext-Tenant-Id'],
      loginName: window.USER_INFO.user.loginName,
      userName: window.USER_INFO.user.name
    }
    report.$upEvent(eventName, { ...basicInfo, ...attrs })
  }
}

Vue.use(ReportInstall)
