import Vue from 'vue'
const blacklist = [
  '/top.json',
  '/sy-lowcode-api/n_menuApi',
  '/tracker-service/event/batchReport',
  '/user/api/menuCallUrlLog/saveMenuCallUrlLog',
  '/tracker-service/event/saveMenuCallUrlLog'
]
let menuApiList = []
let timer = null
// 收集菜单API映射表
export default function collectMenuApi (pathname, method) {
  const tenantId = window.QIANKUN_DATA.active
  const activePageNav = window.QIANKUN_DATA.activePageNav
  if (activePageNav) {
    const { href, code, id } = activePageNav
    const obj = {
      tenantId, // 菜单所属租户编码
      menuId: id, // 菜单id
      menuPath: href, // 菜单路径
      callUrl: pathname, // 调用的接口路径
      methodType: method // 调用类型
    }
    sendAjax(obj)
  }
}
// 发送ajax请求
function sendAjax (obj) {
  menuApiList.push(obj)
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    // Vue.prototype.$utils.axios.post('http://tianmu.syounggroup.com:3005/sy-lowcode-api/n_menuApi', menuApiList)
    Vue.prototype.$utils.axios.post('/tracker-service/event/saveMenuCallUrlLog', menuApiList)
    menuApiList = []
  }, 5 * 1000)
}
ajaxInterceptor()
// ajax请求拦截器
function ajaxInterceptor () {
  const ajax = window.XMLHttpRequest
  const oldOpen = ajax.prototype.open
  //   const oldSend = ajax.prototype.send
  ajax.prototype.open = function (method, url) {
    if (url.indexOf('gw.syounggroup.com/') !== -1) {
      const pathname = getUrlPath(url)
      if (!isBlacklist(getUrlPath(url))) {
        collectMenuApi(pathname, method)
      }
    }
    return oldOpen.apply(this, arguments)
  }
  //   ajax.prototype.send = function () {
  //     const that = this
  //     this.addEventListener('load', function () {
  //       const res = JSON.parse(that.response)
  //     })
  //     return oldSend.apply(this, arguments)
  //   }
}

function getUrlPath (url) {
  return (new URL(url)).pathname
}

function isBlacklist (pathname) {
  return blacklist.includes(pathname)
}
