# HERMES微前端内存优化指南

## 概述

本指南介绍了HERMES微前端管理后台系统的内存泄漏优化方案，包括问题诊断、解决方案实施和使用说明。

## 问题背景

在qiankun微前端架构中，子应用切换时容易出现以下内存泄漏问题：

1. **子应用销毁不完整**：Vue实例、路由钩子、Vuex store未正确清理
2. **第三方库资源泄漏**：xgplayer、wavesurfer、luckysheet等实例未释放
3. **全局事件监听器泄漏**：点击事件、上下文菜单事件等未移除
4. **定时器和异步操作泄漏**：setInterval、setTimeout未清理

## 解决方案架构

### 1. 核心组件

- **enhancedLoadSys.js**: 增强版子应用加载管理器
- **resourceManager.js**: 第三方库资源统一管理器
- **memoryMonitor.js**: 内存监控和预警系统
- **PerformanceMonitor.vue**: 性能监控面板组件

### 2. 优化策略

#### 2.1 子应用生命周期管理

```javascript
// 使用增强版混入替换原有的MINXIN_LOAD_SYS
import enhancedLoadSys from '@/mixins/enhancedLoadSys'

export default {
  mixins: [enhancedLoadSys], // 替换原有混入
  // ...
}
```

#### 2.2 第三方库资源管理

```javascript
import resourceManager from '@/utils/resourceManager'

// 创建视频播放器
const player = await resourceManager.createVideoPlayer({
  id: 'video-container',
  url: 'video.mp4'
}, 'unique-instance-id')

// 自动清理（组件销毁时）
// resourceManager.destroyVideoPlayer('unique-instance-id')
```

#### 2.3 内存监控

```javascript
import memoryMonitor from '@/utils/memoryMonitor'

// 启动监控
memoryMonitor.startMonitoring()

// 配置回调
memoryMonitor.options.onWarning = (memoryInfo) => {
  console.warn('内存使用警告:', memoryInfo.used + 'MB')
}
```

## 使用指南

### 1. 快速开始

#### 1.1 更新主应用

在 `src/App.vue` 中已经集成了优化方案：

```vue
<template>
  <div>
    <!-- 现有内容 -->
    <PerformanceMonitor />
  </div>
</template>

<script>
import enhancedLoadSys from '@/mixins/enhancedLoadSys'
import memoryMonitor from '@/utils/memoryMonitor'

export default {
  mixins: [enhancedLoadSys], // 已替换原有混入
  mounted() {
    this.initMemoryMonitor() // 已添加初始化
  }
}
</script>
```

#### 1.2 性能监控面板

使用快捷键 `Ctrl+Shift+M` 打开性能监控面板，可以：

- 实时查看内存使用情况
- 监控资源统计（子应用、媒体实例、定时器等）
- 手动执行内存清理
- 查看内存使用趋势图表

### 2. 预览组件优化

#### 2.1 使用优化后的预览组件

```vue
<template>
  <div>
    <enhanced-preview ref="preview" />
  </div>
</template>

<script>
import EnhancedPreview from '@/views/app/enhancedPreview.vue'

export default {
  components: { EnhancedPreview },
  methods: {
    previewFile(url) {
      this.$refs.preview.preview(url)
    }
  }
}
</script>
```

#### 2.2 第三方库实例管理

```javascript
// 在组件中使用资源管理器
import resourceManager from '@/utils/resourceManager'

export default {
  async mounted() {
    // 创建播放器实例
    this.player = await resourceManager.createVideoPlayer({
      id: 'player-container',
      url: this.videoUrl
    }, this.instanceId)
  },
  
  beforeDestroy() {
    // 自动清理（也可以手动调用）
    resourceManager.destroyVideoPlayer(this.instanceId)
  }
}
```

### 3. 内存监控配置

#### 3.1 自定义阈值

```javascript
import memoryMonitor from '@/utils/memoryMonitor'

// 自定义配置
memoryMonitor.options = {
  warningThreshold: 600,    // 警告阈值 600MB
  criticalThreshold: 1000,  // 严重阈值 1000MB
  maxThreshold: 1300,       // 最大阈值 1300MB
  monitorInterval: 20000,   // 监控间隔 20秒
  autoCleanup: true         // 启用自动清理
}
```

#### 3.2 注册清理回调

```javascript
// 注册自定义清理逻辑
memoryMonitor.registerCleanupCallback((level) => {
  if (level === 'deep') {
    // 执行深度清理逻辑
    this.clearCustomCache()
  }
}, 'normal')
```

### 4. 开发最佳实践

#### 4.1 组件开发规范

```javascript
export default {
  data() {
    return {
      timers: new Set(),
      eventListeners: new Map()
    }
  },
  
  methods: {
    // 注册定时器
    addTimer(callback, interval) {
      const timer = setInterval(callback, interval)
      this.timers.add(timer)
      return timer
    },
    
    // 注册事件监听器
    addEventListeners(element, event, handler) {
      element.addEventListener(event, handler)
      if (!this.eventListeners.has(element)) {
        this.eventListeners.set(element, new Map())
      }
      this.eventListeners.get(element).set(event, handler)
    }
  },
  
  beforeDestroy() {
    // 清理定时器
    this.timers.forEach(timer => clearInterval(timer))
    this.timers.clear()
    
    // 清理事件监听器
    this.eventListeners.forEach((events, element) => {
      events.forEach((handler, event) => {
        element.removeEventListener(event, handler)
      })
    })
    this.eventListeners.clear()
  }
}
```

#### 4.2 第三方库集成规范

```javascript
// 推荐方式：使用资源管理器
import resourceManager from '@/utils/resourceManager'

export default {
  async mounted() {
    // 通过资源管理器创建实例
    this.instance = await resourceManager.createVideoPlayer(config, this.instanceId)
  },
  
  beforeDestroy() {
    // 资源管理器会自动清理，也可以手动清理
    resourceManager.destroyVideoPlayer(this.instanceId)
  }
}
```

## 监控和调试

### 1. 性能监控面板

- **快捷键**: `Ctrl+Shift+M`
- **功能**: 
  - 实时内存使用情况
  - 资源统计信息
  - 手动清理操作
  - 内存使用历史图表

### 2. 控制台调试

```javascript
// 获取内存信息
console.log(memoryMonitor.getMemoryInfo())

// 获取资源统计
console.log(resourceManager.getResourceStats())

// 获取监控报告
console.log(memoryMonitor.getReport())

// 手动执行清理
memoryMonitor.performDeepCleanup()
```

### 3. 内存泄漏检测

使用Chrome DevTools的Memory面板：

1. 打开DevTools → Memory
2. 选择"Heap snapshot"
3. 在子应用切换前后分别拍摄快照
4. 对比快照找出未释放的对象

## 测试

### 1. 运行测试用例

```bash
npm run test:unit tests/unit/memoryOptimization.spec.js
```

### 2. 手动测试场景

1. **子应用切换测试**：
   - 在不同子应用间快速切换
   - 观察内存使用情况
   - 验证资源是否正确清理

2. **媒体文件预览测试**：
   - 预览多个视频/音频文件
   - 关闭预览窗口
   - 检查播放器实例是否释放

3. **长时间运行测试**：
   - 系统运行数小时
   - 监控内存增长趋势
   - 验证自动清理机制

## 性能指标

### 1. 内存使用优化目标

- **正常使用**: < 800MB
- **警告阈值**: 800MB - 1200MB  
- **严重阈值**: 1200MB - 1500MB
- **最大阈值**: > 1500MB（触发页面刷新）

### 2. 资源清理效果

- **子应用切换**: 内存增长 < 50MB/次
- **媒体预览**: 关闭后内存释放 > 90%
- **长时间运行**: 24小时内存增长 < 200MB

## 故障排除

### 1. 常见问题

**Q: 内存监控面板无法打开**
A: 检查是否正确导入PerformanceMonitor组件，确认快捷键`Ctrl+Shift+M`

**Q: 第三方库实例未正确清理**
A: 确保使用resourceManager创建实例，检查beforeDestroy钩子是否正确调用

**Q: 内存使用仍然过高**
A: 检查是否有未处理的定时器、事件监听器或DOM引用

### 2. 调试技巧

1. 使用`memoryMonitor.getReport()`查看详细信息
2. 检查`resourceManager.getResourceStats()`确认资源清理
3. 在Chrome DevTools中使用Memory面板分析堆快照
4. 启用详细日志输出进行问题定位

## 更新日志

### v1.0.0 (2024-06-24)
- 实现增强版子应用管理器
- 添加第三方库资源管理器
- 集成内存监控和预警系统
- 提供性能监控面板
- 修复pageNav.vue中的拼写错误
- 优化预览组件的资源管理
