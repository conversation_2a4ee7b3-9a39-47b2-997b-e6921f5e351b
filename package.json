{"name": "qiankun-base", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve --mode dev", "prod": "vue-cli-service serve --mode prod", "build:test": "vue-cli-service build --mode test --report", "build:prod": "vue-cli-service build --mode prod --report", "lint": "vue-cli-service lint", "inspect": "vue-cli-service inspect --> output.js"}, "dependencies": {"axios": "^0.21.1", "core-js": "^3.16.1", "docx-preview": "0.1.8", "element-theme-chalk": "^2.15.6", "element-ui": "2.15.1", "lodash": "^4.17.21", "lowcodelib": "1.1.381", "luckyexcel": "^1.0.1", "nprogress": "^0.2.0", "qiankun": "2.10.16", "sortablejs": "^1.14.0", "vue": "2.6.14", "vue-router": "3.6.5", "vuex": "^3.6.2", "wavesurfer.js": "^6.0.0", "xgplayer": "^2.31.4", "xgplayer-flv.js": "^2.3.0", "xgplayer-hls.js": "^2.6.1", "xgplayer-mp4": "^2.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "compression-webpack-plugin": "^6.0.4", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.12.2", "less-loader": "^7.0.2", "svg-sprite-loader": "^6.0.6", "vue-template-compiler": "2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}